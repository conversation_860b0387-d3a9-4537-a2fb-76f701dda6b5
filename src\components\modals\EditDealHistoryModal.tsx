import React, { useState, useEffect } from 'react';
import { X, Save, Loader2 } from 'lucide-react';
import { useWebSocket } from '../../contexts/WebSocketContext';

interface EditDealHistoryModalProps {
  isOpen: boolean;
  deal: any;
  onClose: () => void;
}

const EditDealHistoryModal = ({ isOpen, deal, onClose }: EditDealHistoryModalProps) => {
  const { addAuditEntry } = useWebSocket();
  const [formData, setFormData] = useState({
    executionTime: '',
    accountId: '',
    symbol: '',
    type: 'buy',
    direction: 'in',
    volume: '',
    executionPrice: '',
    orderId: '',
    commission: '',
    swap: '',
    profitLoss: '',
    balance: '',
    comment: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [originalData, setOriginalData] = useState<any>(null);

  useEffect(() => {
    if (deal && isOpen) {
      const dealData = {
        executionTime: deal.executionTime || '',
        accountId: deal.accountId || '',
        symbol: deal.symbol || '',
        type: deal.type || 'buy',
        direction: deal.direction || 'in',
        volume: deal.volume?.toString() || '',
        executionPrice: deal.executionPrice?.toString() || '',
        orderId: deal.orderId || '',
        commission: deal.commission?.toString() || '',
        swap: deal.swap?.toString() || '',
        profitLoss: deal.profitLoss?.toString() || '',
        balance: deal.balance?.toString() || '',
        comment: deal.comment || ''
      };
      setFormData(dealData);
      setOriginalData(dealData);
      setErrors({});
    }
  }, [deal, isOpen]);

  // ESC key handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  const handleSubmit = async () => {
    setErrors({});
    
    // Validation
    const newErrors: Record<string, string> = {};
    if (!formData.executionTime) newErrors.executionTime = 'Execution time is required';
    if (!formData.accountId) newErrors.accountId = 'Account ID is required';
    if (!formData.type) newErrors.type = 'Type is required';
    if (formData.volume && (isNaN(Number(formData.volume)) || Number(formData.volume) <= 0)) {
      newErrors.volume = 'Please enter a valid volume';
    }
    if (formData.executionPrice && isNaN(Number(formData.executionPrice))) {
      newErrors.executionPrice = 'Please enter a valid execution price';
    }
    if (formData.commission && isNaN(Number(formData.commission))) {
      newErrors.commission = 'Please enter a valid commission amount';
    }
    if (formData.swap && isNaN(Number(formData.swap))) {
      newErrors.swap = 'Please enter a valid swap amount';
    }
    if (formData.profitLoss && isNaN(Number(formData.profitLoss))) {
      newErrors.profitLoss = 'Please enter a valid profit/loss amount';
    }
    if (formData.balance && isNaN(Number(formData.balance))) {
      newErrors.balance = 'Please enter a valid balance amount';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Find what changed for audit trail
      const changes: { field: string; from: any; to: any }[] = [];
      Object.keys(formData).forEach(key => {
        if (formData[key as keyof typeof formData] !== originalData[key]) {
          changes.push({
            field: key.charAt(0).toUpperCase() + key.slice(1),
            from: originalData[key] || 'None',
            to: formData[key as keyof typeof formData] || 'None'
          });
        }
      });

      if (changes.length > 0) {
        // Add audit entry for deal modification
        addAuditEntry({
          action: 'EDIT',
          entityType: 'Deal',
          entityId: deal.dealId,
          accountId: formData.accountId,
          symbol: formData.symbol,
          details: `Modified deal ${deal.dealId}: ${changes.map(c => `${c.field} changed from ${c.from} to ${c.to}`).join(', ')}`,
          changes
        });
      }
      
      console.log('Deal updated:', formData);
      onClose();
    } catch (error) {
      setErrors({ general: 'Failed to update deal record' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700">
          <h3 className="text-lg font-semibold text-white">Edit Deal Record</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <div className="p-4 space-y-4">
          {errors.general && (
            <div className="p-3 bg-red-900/30 border border-red-800 rounded text-red-400 text-sm">
              {errors.general}
            </div>
          )}

          {/* Warning Message */}
          <div className="p-3 bg-yellow-900/30 border border-yellow-800 rounded">
            <div className="flex items-start space-x-2">
              <div className="text-yellow-400 mt-0.5">⚠️</div>
              <div className="text-yellow-400 text-sm">
                <strong>Important:</strong> Editing deal records will create an audit trail entry. 
                This action modifies historical transaction data and should only be performed for corrections 
                or compliance purposes. All changes will be logged with timestamp and user details.
              </div>
            </div>
          </div>

          {/* Deal ID (Read-only) */}
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Deal ID</label>
            <input
              type="text"
              value={deal?.dealId?.startsWith('DEAL') ? deal.dealId : `DEAL${deal?.dealId}`}
              disabled
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-gray-400 cursor-not-allowed"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Execution Time *</label>
              <input
                type="datetime-local"
                value={formData.executionTime.replace(' ', 'T')}
                onChange={(e) => setFormData({ ...formData, executionTime: e.target.value.replace('T', ' ') })}
                className={`w-full px-3 py-2 bg-slate-700 border rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.executionTime ? 'border-red-500' : 'border-slate-600'
                }`}
              />
              {errors.executionTime && <p className="text-red-400 text-xs mt-1">{errors.executionTime}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Account ID *</label>
              <select
                value={formData.accountId}
                onChange={(e) => setFormData({ ...formData, accountId: e.target.value })}
                className={`w-full px-3 py-2 bg-slate-700 border rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.accountId ? 'border-red-500' : 'border-slate-600'
                }`}
              >
                <option value="">Select account...</option>
                <option value="ACC001">ACC001</option>
                <option value="ACC002">ACC002</option>
                <option value="ACC003">ACC003</option>
                <option value="ACC004">ACC004</option>
                <option value="ACC005">ACC005</option>
                <option value="ACC006">ACC006</option>
                <option value="ACC007">ACC007</option>
              </select>
              {errors.accountId && <p className="text-red-400 text-xs mt-1">{errors.accountId}</p>}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Symbol</label>
              <select
                value={formData.symbol}
                onChange={(e) => setFormData({ ...formData, symbol: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select symbol...</option>
                <option value="EURUSD">EURUSD</option>
                <option value="GBPUSD">GBPUSD</option>
                <option value="USDJPY">USDJPY</option>
                <option value="AUDUSD">AUDUSD</option>
                <option value="USDCAD">USDCAD</option>
                <option value="EURGBP">EURGBP</option>
                <option value="EURJPY">EURJPY</option>
                <option value="GBPJPY">GBPJPY</option>
                <option value="XAUUSD">XAUUSD</option>
                <option value="XAGUSD">XAGUSD</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Order ID</label>
              <input
                type="text"
                placeholder="Order or Position ID"
                value={formData.orderId}
                onChange={(e) => setFormData({ ...formData, orderId: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Type *</label>
              <select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="balance">Balance</option>
                <option value="buy">Buy</option>
                <option value="sell">Sell</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Direction</label>
              <select
                value={formData.direction}
                onChange={(e) => setFormData({ ...formData, direction: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={formData.type === 'balance'}
              >
                <option value="">None</option>
                <option value="in">In</option>
                <option value="out">Out</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Volume</label>
              <input
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData.volume}
                onChange={(e) => setFormData({ ...formData, volume: e.target.value })}
                className={`w-full px-3 py-2 bg-slate-700 border rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.volume ? 'border-red-500' : 'border-slate-600'
                }`}
                disabled={formData.type === 'balance'}
              />
              {errors.volume && <p className="text-red-400 text-xs mt-1">{errors.volume}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Execution Price</label>
              <input
                type="number"
                step="0.00001"
                placeholder="0.00000"
                value={formData.executionPrice}
                onChange={(e) => setFormData({ ...formData, executionPrice: e.target.value })}
                className={`w-full px-3 py-2 bg-slate-700 border rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.executionPrice ? 'border-red-500' : 'border-slate-600'
                }`}
                disabled={formData.type === 'balance'}
              />
              {errors.executionPrice && <p className="text-red-400 text-xs mt-1">{errors.executionPrice}</p>}
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Commission</label>
              <input
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData.commission}
                onChange={(e) => setFormData({ ...formData, commission: e.target.value })}
                className={`w-full px-3 py-2 bg-slate-700 border rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.commission ? 'border-red-500' : 'border-slate-600'
                }`}
              />
              {errors.commission && <p className="text-red-400 text-xs mt-1">{errors.commission}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Swap</label>
              <input
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData.swap}
                onChange={(e) => setFormData({ ...formData, swap: e.target.value })}
                className={`w-full px-3 py-2 bg-slate-700 border rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.swap ? 'border-red-500' : 'border-slate-600'
                }`}
              />
              {errors.swap && <p className="text-red-400 text-xs mt-1">{errors.swap}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Profit/Loss</label>
              <input
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData.profitLoss}
                onChange={(e) => setFormData({ ...formData, profitLoss: e.target.value })}
                className={`w-full px-3 py-2 bg-slate-700 border rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.profitLoss ? 'border-red-500' : 'border-slate-600'
                }`}
              />
              {errors.profitLoss && <p className="text-red-400 text-xs mt-1">{errors.profitLoss}</p>}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Balance</label>
            <input
              type="number"
              step="0.01"
              placeholder="0.00"
              value={formData.balance}
              onChange={(e) => setFormData({ ...formData, balance: e.target.value })}
              className={`w-full px-3 py-2 bg-slate-700 border rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.balance ? 'border-red-500' : 'border-slate-600'
              }`}
            />
            {errors.balance && <p className="text-red-400 text-xs mt-1">{errors.balance}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Comment</label>
            <input
              type="text"
              placeholder="Optional comment"
              value={formData.comment}
              onChange={(e) => setFormData({ ...formData, comment: e.target.value })}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-4 border-t border-slate-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Saving...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Save Changes</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditDealHistoryModal; 