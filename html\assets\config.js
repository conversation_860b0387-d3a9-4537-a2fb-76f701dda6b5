// Configuration file for the Broker Demo
// Edit this file to customize the demo for different companies

const BrokerConfig = {
  // Company Branding
  company: {
    name: "OTX Platform",
    tagline: "Complete Trading Infrastructure", 
    logo: "OTX", // Text logo or path to image
    domain: "otxplatform.com",
    year: "2024"
  },

  // Colors & Theme
  theme: {
    primary: "from-blue-500 to-cyan-500",
    primaryHover: "from-blue-600 to-cyan-600",
    background: "from-slate-900 via-blue-900 to-slate-900"
  },

  // Hero Section
  hero: {
    badge: "🚀 Launch Your Trading Business Today",
    title: "Complete Trading",
    titleHighlight: " Platform Solution",
    description: "Everything you need to start and scale your brokerage business. CRM, mobile trading app, and professional dealer terminal - all integrated and ready to deploy.",
    primaryCTA: "Start Your Brokerage",
    secondaryCTA: "View Demo"
  },

  // Platform Features
  features: [
    {
      icon: "👥",
      title: "Complete CRM System",
      description: "Manage clients, leads, and relationships with our comprehensive CRM platform designed for trading businesses."
    },
    {
      icon: "📱",
      title: "Mobile Trading App", 
      description: "White-label mobile application for your clients with real-time trading, charts, and portfolio management."
    },
    {
      icon: "🖥️",
      title: "Professional Dealer Terminal",
      description: "Advanced dealer terminal for risk management, position monitoring, and real-time market operations."
    },
    {
      icon: "🛡️",
      title: "Enterprise Security",
      description: "Bank-grade security with multi-factor authentication, encryption, and compliance features."
    },
    {
      icon: "⚡",
      title: "Lightning Fast Setup", 
      description: "Get your trading business operational in minutes, not months. Complete platform deployment in under 24 hours."
    },
    {
      icon: "📊",
      title: "Advanced Analytics",
      description: "Comprehensive reporting, risk analytics, and business intelligence tools to grow your brokerage."
    }
  ],

  // Pricing Plans
  plans: [
    {
      name: "Starter",
      price: "$2,999",
      period: "/month",
      description: "Perfect for new brokers starting their trading business",
      popular: false,
      features: [
        "Up to 100 client accounts",
        "Basic CRM functionality", 
        "Mobile trading app",
        "Dealer terminal access",
        "Email support",
        "Standard reporting"
      ]
    },
    {
      name: "Professional",
      price: "$5,999", 
      period: "/month",
      description: "Ideal for growing brokerages with advanced needs",
      popular: true,
      features: [
        "Up to 500 client accounts",
        "Advanced CRM with automation",
        "White-label mobile app", 
        "Multi-dealer terminal",
        "24/7 phone support",
        "Advanced analytics",
        "Custom integrations",
        "Risk management tools"
      ]
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "",
      description: "Tailored solutions for large-scale operations", 
      popular: false,
      features: [
        "Unlimited client accounts",
        "Full platform customization",
        "Dedicated infrastructure",
        "Multiple trading venues", 
        "Dedicated account manager",
        "Custom development",
        "SLA guarantees",
        "Regulatory compliance"
      ]
    }
  ],

  // Payment & Billing
  payment: {
    setupFee: "Free",
    firstMonthDiscount: "50% Off",
    guarantees: [
      "30-day money-back guarantee",
      "Cancel anytime", 
      "24/7 support included"
    ],
    security: "SSL Secured"
  },

  // Configuration Options
  configuration: {
    userGroups: [
      { value: "1", label: "1 user group" },
      { value: "2", label: "2 user groups" },
      { value: "3", label: "3 user groups" },
      { value: "4", label: "4 user groups" },
      { value: "5", label: "5 user groups" }
    ],
    traderAccounts: [
      { value: "500", label: "500 accounts" }, 
      { value: "600", label: "600 accounts" },
      { value: "700", label: "700 accounts" },
      { value: "800", label: "800 accounts" },
      { value: "900", label: "900 accounts" },
      { value: "1000", label: "1000 accounts" }
    ],
    instruments: [
      {
        id: "forex-majors",
        title: "Forex Majors",
        icon: "🌍",
        popular: true,
        description: "EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD, USD/CAD, NZD/USD"
      },
      {
        id: "forex-minors", 
        title: "Forex Minors",
        icon: "🌍",
        popular: false,
        description: "EUR/GBP, EUR/JPY, GBP/JPY, AUD/CAD, EUR/AUD, GBP/CAD"
      },
      {
        id: "precious-metals",
        title: "Precious Metals", 
        icon: "🪙",
        popular: true,
        description: "Gold (XAU/USD), Silver (XAG/USD), Platinum, Palladium"
      }
    ]
  },

  // External Links & Credentials
  platforms: {
    crm: {
      name: "CRM Admin Portal",
      description: "Manage clients, leads, and business operations", 
      url: "https://crmdkdev.m-finance.net/admin-portal/home/<USER>",
      username: "admin@{company}.com", // {company} will be replaced
      password: "Demo123!"
    },
    dealer: {
      name: "Dealer Terminal",
      description: "Professional trading and risk management",
      url: "https://otx.mfcloud.net:2096/dealer/",
      username: "dealer@{company}.com", // {company} will be replaced  
      password: "Dealer123!"
    }
  },

  // Quick Start Guide
  quickStart: [
    {
      title: "Access CRM Admin Portal",
      description: "Set up your client management system and configure user groups",
      action: "Open CRM",
      status: "completed"
    },
    {
      title: "Configure Dealer Terminal", 
      description: "Set up trading parameters and risk management settings",
      action: "Open Dealer",
      status: "completed"
    },
    {
      title: "Customize Mobile App",
      description: "Brand your mobile trading app with company logo and colors",
      action: "Customize", 
      status: "coming-soon"
    },
    {
      title: "Set Up Payment Gateway",
      description: "Configure payment processing for client deposits and withdrawals",
      action: "Configure",
      status: "coming-soon"
    }
  ],

  // Support & Resources
  support: {
    email: "<EMAIL>",
    documentation: "https://docs.otxplatform.com",
    training: "https://calendly.com/otxplatform/training",
    phone: "+****************"
  },

  // Legal & Footer
  legal: {
    termsConditions: "By completing this purchase, you agree to our Terms of Service and Privacy Policy.",
    autoRenewal: "Your subscription will auto-renew monthly. Cancel anytime.", 
    copyright: "All rights reserved. Complete trading infrastructure for modern brokerages."
  }
};

// Helper functions for dynamic content
const BrokerUtils = {
  // Generate company-specific email from company name
  generateEmail: function(template, companyName) {
    const cleanCompany = companyName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
    return template.replace('{company}', cleanCompany);
  },

  // Get selected plan or default to Professional
  getSelectedPlan: function() {
    const params = new URLSearchParams(window.location.search);
    const planName = params.get('plan') || 'Professional';
    return BrokerConfig.plans.find(p => p.name === planName) || BrokerConfig.plans[1];
  },

  // Calculate discounted price for first month
  calculateTotal: function(plan) {
    if (plan.price === 'Custom') return 'Custom';
    const price = parseInt(plan.price.replace('$', '').replace(',', ''));
    return '$' + (price * 0.5).toLocaleString(); // 50% off first month
  },

  // Generate subscription ID
  generateSubId: function() {
    return 'SUB_' + Date.now();
  }
};