import React, { useState, useEffect } from 'react';
import { X, Loader2 } from 'lucide-react';
import { useWebSocket } from '../../contexts/WebSocketContext';
import { formatPrice } from '../../utils/priceFormatting';

interface ModifyPositionModalProps {
  isOpen: boolean;
  position: any;
  onClose: () => void;
}

const ModifyPositionModal = ({ isOpen, position, onClose }: ModifyPositionModalProps) => {
  const { updatePosition, tradingDisabledSymbols, allTradingDisabled } = useWebSocket();
  const [formData, setFormData] = useState({
    sl: position?.sl || '',
    tp: position?.tp || ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // ESC key handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  // Check if trading is disabled for this symbol
  const isTradingDisabled = allTradingDisabled || (position?.symbol && tradingDisabledSymbols.has(position.symbol));

  const handleSubmit = async () => {
    setErrors({});
    
    // Check for trading restrictions first
    if (isTradingDisabled) {
      setErrors({ general: 'Trading is currently disabled for this symbol' });
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Update position in context
      const updates: { sl?: number; tp?: number } = {};
      if (formData.sl) updates.sl = Number(formData.sl);
      if (formData.tp) updates.tp = Number(formData.tp);
      
      updatePosition(position.positionId, updates);
      console.log('Position modified:', { positionId: position.positionId, ...updates });
      onClose();
    } catch (error) {
      setErrors({ general: 'Stop level too close to market price' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen || !position) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700">
          <h3 className="text-lg font-semibold text-white">Modify Position</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <div className="p-4 space-y-4">
          {errors.general && (
            <div className="p-3 bg-red-900/30 border border-red-800 rounded text-red-400 text-sm">
              {errors.general}
            </div>
          )}

          {/* Trading Disabled Warning */}
          {isTradingDisabled && (
            <div className="bg-red-900/20 border border-red-800 rounded-lg p-3">
              <div className="text-red-400 text-sm text-center flex items-center justify-center space-x-2">
                <span>🚫</span>
                <span>
                  Trading is currently disabled {position?.symbol ? `for ${position.symbol}` : 'globally'}
                </span>
              </div>
            </div>
          )}

          {/* Position Info */}
          <div className="bg-slate-700 rounded-lg p-3 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Position ID:</span>
              <span className="text-white">{position.positionId}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Symbol:</span>
              <span className="text-white font-medium">{position.symbol}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Current Price:</span>
              <span className="text-white font-mono">{formatPrice(position.currentPrice, position.symbol)}</span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Stop Loss</label>
              <input
                type="number"
                step="0.00001"
                placeholder={`Current: ${position.sl || '—'}`}
                value={formData.sl}
                onChange={(e) => setFormData({ ...formData, sl: e.target.value })}
                disabled={isTradingDisabled}
                className={`w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isTradingDisabled ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Take Profit</label>
              <input
                type="number"
                step="0.00001"
                placeholder={`Current: ${position.tp || '—'}`}
                value={formData.tp}
                onChange={(e) => setFormData({ ...formData, tp: e.target.value })}
                disabled={isTradingDisabled}
                className={`w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isTradingDisabled ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-4 border-t border-slate-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || isTradingDisabled}
            className={`px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 ${
              isTradingDisabled ? 'bg-gray-600' : ''
            }`}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Updating...</span>
              </>
            ) : (
              <span>Confirm</span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ModifyPositionModal;
