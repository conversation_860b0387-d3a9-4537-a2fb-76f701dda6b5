import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useWebSocket } from '../../contexts/WebSocketContext';
import { Plus, Edit, X, Copy, Eye, BarChart3 } from 'lucide-react';
import NewPositionModal from '../modals/NewPositionModal';
import ModifyPositionModal from '../modals/ModifyPositionModal';
import ClosePositionModal from '../modals/ClosePositionModal';
import { formatPrice, formatPriceOrFallback } from '../../utils/priceFormatting';
import SortableTable, { Column } from '../ui/sortable-table';
import { Position, ContextMenuState, PositionFilters } from '../../types/trading';

const OpenPositionsTab = () => {
  const { positions, traderAccounts } = useWebSocket();
  const [showNewPosition, setShowNewPosition] = useState(false);
  const [modifyingPosition, setModifyingPosition] = useState<Position | null>(null);
  const [closingPosition, setClosingPosition] = useState<Position | null>(null);
  const [contextMenu, setContextMenu] = useState<ContextMenuState | null>(null);
  const [filters, setFilters] = useState<PositionFilters>({
    userGroup: 'all',
    accountId: '',
    symbol: ''
  });

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+N for new position
      if (event.ctrlKey && event.key === 'n') {
        event.preventDefault();
        setShowNewPosition(true);
      }
      // Escape to close context menu
      if (event.key === 'Escape') {
        setContextMenu(null);
      }
    };

    const handleClickOutside = () => {
      setContextMenu(null);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('click', handleClickOutside);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // Right-click context menu handler
  const handleContextMenu = useCallback((event: React.MouseEvent, position: Position) => {
    event.preventDefault();
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      position
    });
  }, []);

  // Double-click handler for quick modify
  const handleDoubleClick = useCallback((position: Position) => {
    setModifyingPosition(position);
  }, []);

  // Context menu actions
  const handleContextMenuAction = (action: string, position: Position) => {
    setContextMenu(null);
    
    switch (action) {
      case 'modify':
        setModifyingPosition(position);
        break;
      case 'close':
        setClosingPosition(position);
        break;
      case 'copy':
        navigator.clipboard.writeText(`${position.symbol} ${position.type} ${position.volume} lots at ${position.openPrice}`);
        break;
      case 'details':
        // Could open a detailed view modal
        console.log('Show details for position:', position.positionId);
        break;
    }
  };

  // Filter positions based on current filters
  const filteredPositions = useMemo(() => {
    return positions.filter(position => {
      // User Group filter - look up the actual user group from traderAccounts
      if (filters.userGroup !== 'all') {
        const account = traderAccounts.find(acc => acc.accountId === position.accountId);
        if (account) {
          if (filters.userGroup === 'premium' && account.userGroup !== 'Premium Clients') return false;
          if (filters.userGroup === 'standard' && account.userGroup !== 'Standard Clients') return false;
        }
      }

      // Account ID filter
      if (filters.accountId && !position.accountId.toLowerCase().includes(filters.accountId.toLowerCase())) {
        return false;
      }

      // Symbol filter
      if (filters.symbol && !position.symbol.toLowerCase().includes(filters.symbol.toLowerCase())) {
        return false;
      }

      return true;
    });
  }, [positions, filters, traderAccounts]);

  // Define table columns
  const columns: Column<Position>[] = useMemo(() => [
    {
      key: 'accountId',
      header: 'Account ID',
      sortable: true,
      className: 'font-medium'
    },
    {
      key: 'positionId',
      header: 'Position ID',
      sortable: true,
      className: 'font-medium'
    },
    {
      key: 'symbol',
      header: 'Symbol',
      sortable: true,
      className: 'font-medium'
    },
    {
      key: 'type',
      header: 'Type',
      sortable: true,
      render: (value, position) => (
        <span 
          className={`px-2 py-1 rounded text-xs ${
            value === 'BUY' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
          }`} 
          aria-label={`${value} position`}
        >
          {value}
        </span>
      ),
      ariaLabel: (value) => `${value} position`
    },
    {
      key: 'volume',
      header: 'Volume',
      sortable: true,
      ariaLabel: (value) => `Volume: ${value} lots`
    },
    {
      key: 'openPrice',
      header: 'Open Price',
      sortable: true,
      className: 'font-mono',
      render: (value, position) => formatPrice(value, position.symbol),
      ariaLabel: (value, position) => `Open price: ${formatPrice(value, position.symbol)}`
    },
    {
      key: 'sl',
      header: 'S/L',
      sortable: true,
      className: 'font-mono',
      render: (value, position) => formatPriceOrFallback(value, position.symbol),
      ariaLabel: (value, position) => `Stop loss: ${formatPriceOrFallback(value, position.symbol)}`
    },
    {
      key: 'tp',
      header: 'T/P',
      sortable: true,
      className: 'font-mono',
      render: (value, position) => formatPriceOrFallback(value, position.symbol),
      ariaLabel: (value, position) => `Take profit: ${formatPriceOrFallback(value, position.symbol)}`
    },
    {
      key: 'currentPrice',
      header: 'Current Price',
      sortable: true,
      className: 'font-mono',
      render: (value, position) => formatPrice(value, position.symbol),
      ariaLabel: (value, position) => `Current price: ${formatPrice(value, position.symbol)}`
    },
    {
      key: 'pl',
      header: 'P/L',
      sortable: true,
      className: 'font-mono',
      render: (value) => (
        <span 
          className={value >= 0 ? 'text-green-400' : 'text-red-400'}
          aria-label={`${value >= 0 ? 'Profit' : 'Loss'}: ${Math.abs(value).toFixed(2)} dollars`}
        >
          {value >= 0 ? '+' : ''}${value.toFixed(2)}
        </span>
      ),
      ariaLabel: (value) => `${value >= 0 ? 'Profit' : 'Loss'}: ${Math.abs(value).toFixed(2)} dollars`
    },
    {
      key: 'openTime',
      header: 'Open Time',
      sortable: true,
      className: 'text-gray-400',
      ariaLabel: (value) => `Opened: ${value}`
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, position) => (
        <div className="flex items-center space-x-1 sm:space-x-2" role="group" aria-label="Position actions">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setModifyingPosition(position);
            }}
            className="p-1 text-blue-400 hover:text-blue-300 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:ring-offset-slate-900 rounded"
            title="Modify (or double-click row)"
            aria-label={`Modify position ${position.positionId}`}
          >
            <Edit className="w-3 h-3 sm:w-4 sm:h-4" aria-hidden="true" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setClosingPosition(position);
            }}
            className="p-1 text-red-400 hover:text-red-300 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 focus:ring-offset-slate-900 rounded"
            title="Close (or right-click for menu)"
            aria-label={`Close position ${position.positionId}`}
          >
            <X className="w-3 h-3 sm:w-4 sm:h-4" aria-hidden="true" />
          </button>
        </div>
      )
    }
  ], [setModifyingPosition, setClosingPosition]);

  return (
    <div className="h-full flex flex-col">
      {/* Header with Filters and New Position Button */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-3 flex-shrink-0 mb-4">
        {/* Title and New Position Button */}
      <div className="flex items-center justify-between">
        <h3 id="positions-heading" className="text-lg font-semibold text-white">Open Positions</h3>
        <button
          onClick={() => setShowNewPosition(true)}
            className="flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors lg:ml-4 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-slate-900"
            title="New Position (Ctrl+N)"
            aria-label="Create new trading position (Ctrl+N)"
        >
          <Plus className="w-4 h-4" aria-hidden="true" />
          <span className="text-sm">New Position</span>
        </button>
      </div>

        {/* Inline Filters */}
        <div className="flex flex-wrap gap-2 lg:gap-3" role="search" aria-labelledby="filters-label">
          <span id="filters-label" className="sr-only">Filter positions</span>
          <div className="min-w-0 flex-1 lg:flex-none lg:w-32">
            <label htmlFor="user-group-filter" className="block text-xs font-medium text-gray-400 mb-1">User Group</label>
          <select
            id="user-group-filter"
            value={filters.userGroup}
            onChange={(e) => setFilters({ ...filters, userGroup: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:ring-offset-slate-900"
              aria-describedby="user-group-help"
          >
            <option value="all">All Groups</option>
            <option value="premium">Premium Clients</option>
            <option value="standard">Standard Clients</option>
          </select>
          <span id="user-group-help" className="sr-only">Filter positions by user group type</span>
        </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-40">
            <label htmlFor="account-id-filter" className="block text-xs font-medium text-gray-400 mb-1">Account ID</label>
          <input
            id="account-id-filter"
            type="text"
            placeholder="Search account..."
            value={filters.accountId}
            onChange={(e) => setFilters({ ...filters, accountId: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:ring-offset-slate-900"
              aria-describedby="account-id-help"
          />
          <span id="account-id-help" className="sr-only">Search positions by account ID</span>
        </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-32">
            <label htmlFor="symbol-filter" className="block text-xs font-medium text-gray-400 mb-1">Symbol</label>
          <input
            id="symbol-filter"
            type="text"
            placeholder="e.g. EURUSD"
            value={filters.symbol}
            onChange={(e) => setFilters({ ...filters, symbol: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:ring-offset-slate-900"
              aria-describedby="symbol-help"
          />
          <span id="symbol-help" className="sr-only">Search positions by trading symbol</span>
          </div>
        </div>
      </div>

      {/* Positions Table - Fill remaining height */}
      <SortableTable
        data={filteredPositions}
        columns={columns}
        onRowDoubleClick={handleDoubleClick}
        onRowContextMenu={handleContextMenu}
        emptyMessage="No positions found."
        emptyDescription="Try adjusting your filters or create a new position."
        tableId="positions-table"
        ariaLabel="Open trading positions"
        ariaDescription={`Open trading positions table showing ${filteredPositions.length} positions with account details, symbols, volumes, prices, and profit/loss information. Use arrow keys to navigate, right-click for actions, or double-click to modify.`}
        getRowId={(position) => position.positionId}
        rowClassName={(position) => position.pl >= 0 ? '' : ''}
      />

      {/* Modals */}
      {showNewPosition && (
        <NewPositionModal
          isOpen={showNewPosition}
          onClose={() => setShowNewPosition(false)}
        />
      )}
      
      {modifyingPosition && (
        <ModifyPositionModal
          isOpen={!!modifyingPosition}
          position={modifyingPosition}
          onClose={() => setModifyingPosition(null)}
        />
      )}
      
      {closingPosition && (
        <ClosePositionModal
          isOpen={!!closingPosition}  
          position={closingPosition}
          onClose={() => setClosingPosition(null)}
        />
      )}

      {/* Context Menu */}
      {contextMenu && (
        <div
          className="fixed bg-slate-800 border border-slate-600 rounded-lg shadow-xl z-50 py-1 min-w-[160px]"
          style={{
            left: `${contextMenu.x}px`,
            top: `${contextMenu.y}px`,
          }}
          onClick={(e) => e.stopPropagation()}
          role="menu"
          aria-label={`Actions for position ${contextMenu.position.positionId}`}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              setContextMenu(null);
            }
          }}
        >
          <button
            onClick={() => handleContextMenuAction('modify', contextMenu.position)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2 focus:outline-none focus:bg-slate-700 focus:ring-2 focus:ring-blue-500 focus:ring-inset"
            role="menuitem"
            aria-label={`Modify position ${contextMenu.position.positionId} for ${contextMenu.position.symbol}`}
          >
            <Edit className="w-4 h-4" aria-hidden="true" />
            <span>Modify Position</span>
          </button>
          <button
            onClick={() => handleContextMenuAction('close', contextMenu.position)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2 focus:outline-none focus:bg-slate-700 focus:ring-2 focus:ring-red-500 focus:ring-inset"
            role="menuitem"
            aria-label={`Close position ${contextMenu.position.positionId} for ${contextMenu.position.symbol}`}
          >
            <X className="w-4 h-4" aria-hidden="true" />
            <span>Close Position</span>
          </button>
          <div className="border-t border-slate-600 my-1" role="separator"></div>
          <button
            onClick={() => handleContextMenuAction('copy', contextMenu.position)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2 focus:outline-none focus:bg-slate-700 focus:ring-2 focus:ring-blue-500 focus:ring-inset"
            role="menuitem"
            aria-label={`Copy details for position ${contextMenu.position.positionId}`}
          >
            <Copy className="w-4 h-4" aria-hidden="true" />
            <span>Copy Details</span>
          </button>
          <button
            onClick={() => handleContextMenuAction('details', contextMenu.position)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2 focus:outline-none focus:bg-slate-700 focus:ring-2 focus:ring-blue-500 focus:ring-inset"
            role="menuitem"
            aria-label={`View details for position ${contextMenu.position.positionId}`}
          >
            <Eye className="w-4 h-4" aria-hidden="true" />
            <span>View Details</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default OpenPositionsTab;
