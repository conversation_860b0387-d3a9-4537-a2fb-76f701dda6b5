import React, { useState, useEffect, useRef } from 'react';
import { X, Loader2 } from 'lucide-react';
import { useWebSocket } from '../../contexts/WebSocketContext';
import { formatPrice, getSymbolPrecision, getSymbolStep } from '../../utils/priceFormatting';

interface NewOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const NewOrderModal = ({ isOpen, onClose }: NewOrderModalProps) => {
  const { quotes, tradingDisabledSymbols, allTradingDisabled, addOrder, addAuditEntry } = useWebSocket();
  const [formData, setFormData] = useState({
    accountId: '',
    symbol: '',
    type: 'BUY',
    orderType: 'LIMIT',
    volume: '',
    price: '',
    sl: '',
    tp: '',
    comment: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const previousActiveElementRef = useRef<HTMLElement | null>(null);

  // Focus management
  useEffect(() => {
    if (isOpen) {
      // Store the previously active element
      previousActiveElementRef.current = document.activeElement as HTMLElement;
      
      // Focus the modal
      setTimeout(() => {
        modalRef.current?.focus();
      }, 100);
    } else {
      // Return focus to the previously active element
      if (previousActiveElementRef.current) {
        previousActiveElementRef.current.focus();
      }
    }
  }, [isOpen]);

  // ESC key handler and focus trapping
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
      
      // Focus trapping
      if (event.key === 'Tab' && isOpen && modalRef.current) {
        const focusableElements = modalRef.current.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        if (event.shiftKey) {
          if (document.activeElement === firstElement) {
            event.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  // Find current quote for selected symbol
  const currentQuote = quotes.find(q => q.symbol === formData.symbol);
  
  // Check if trading is disabled for this symbol
  const isTradingDisabled = allTradingDisabled || (formData.symbol && tradingDisabledSymbols.has(formData.symbol));

  const handleSubmit = async () => {
    setErrors({});
    
    // Check for trading restrictions first
    if (isTradingDisabled) {
      setErrors({ general: 'Trading is currently disabled for this symbol' });
      return;
    }
    
    // Validation
    const newErrors: Record<string, string> = {};
    if (!formData.accountId) newErrors.accountId = 'Account ID is required';
    if (!formData.symbol) newErrors.symbol = 'Symbol is required';
    if (!formData.volume) newErrors.volume = 'Volume is required';
    if (!formData.price) newErrors.price = 'Price is required';

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Determine order type string
      const orderTypeString = `${formData.type} ${formData.orderType}`;
      
      // Use specified price
      const orderPrice = Number(formData.price);
      
      // Create the new order
      addOrder({
        accountId: formData.accountId,
        symbol: formData.symbol,
        type: orderTypeString,
        volume: Number(formData.volume),
        orderPrice: Number(orderPrice.toFixed(getSymbolPrecision(formData.symbol))),
        sl: formData.sl ? Number(formData.sl) : undefined,
        tp: formData.tp ? Number(formData.tp) : undefined,
        placementTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        expiration: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
      });
      
      console.log('New order placed:', formData);
      onClose();
      
      // Reset form
      setFormData({
        accountId: '',
        symbol: '',
        type: 'BUY',
        orderType: 'LIMIT',
        volume: '',
        price: '',
        sl: '',
        tp: '',
        comment: ''
      });
    } catch (error) {
      setErrors({ general: 'Failed to place order' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <div 
        ref={modalRef}
        className="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-lg max-h-[90vh] overflow-y-auto focus:outline-none"
        tabIndex={-1}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700">
          <h3 id="modal-title" className="text-lg font-semibold text-white">New Order</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800 rounded"
            aria-label="Close new order modal"
          >
            <X className="w-5 h-5" aria-hidden="true" />
          </button>
        </div>

        {/* Form */}
        <div className="p-4 space-y-4">
          <div id="modal-description" className="sr-only">
            Create a new trading order by selecting an account, symbol, order type, volume, and price. All required fields are marked with an asterisk.
          </div>
          
          {errors.general && (
            <div className="p-3 bg-red-900/30 border border-red-800 rounded text-red-400 text-sm" role="alert" aria-live="polite">
              {errors.general}
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="account-id" className="block text-sm font-medium text-gray-400 mb-1">Account ID *</label>
              <select
                id="account-id"
                value={formData.accountId}
                onChange={(e) => setFormData({ ...formData, accountId: e.target.value })}
                className={`w-full px-3 py-2 bg-slate-700 border rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800 ${
                  errors.accountId ? 'border-red-500' : 'border-slate-600'
                }`}
                aria-invalid={errors.accountId ? 'true' : 'false'}
                aria-describedby={errors.accountId ? 'account-id-error' : undefined}
                required
              >
                <option value="">Select account...</option>
                <option value="ACC001">ACC001 - John Doe</option>
                <option value="ACC002">ACC002 - Jane Smith</option>
                <option value="ACC003">ACC003 - Mike Johnson</option>
                <option value="ACC004">ACC004 - Sarah Wilson</option>
                <option value="ACC005">ACC005 - David Brown</option>
                <option value="ACC006">ACC006 - Lisa Davis</option>
                <option value="ACC007">ACC007 - Tom Miller</option>
              </select>
              {errors.accountId && <p id="account-id-error" className="text-red-400 text-xs mt-1" role="alert">{errors.accountId}</p>}
            </div>

            <div>
              <label htmlFor="symbol" className="block text-sm font-medium text-gray-400 mb-1">Symbol *</label>
              <select
                id="symbol"
                value={formData.symbol}
                onChange={(e) => setFormData({ ...formData, symbol: e.target.value })}
                className={`w-full px-3 py-2 bg-slate-700 border rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800 ${
                  errors.symbol ? 'border-red-500' : 'border-slate-600'
                }`}
                aria-invalid={errors.symbol ? 'true' : 'false'}
                aria-describedby={errors.symbol ? 'symbol-error' : undefined}
                required
              >
                <option value="">Select symbol...</option>
                <option value="EURUSD">EURUSD</option>
                <option value="GBPUSD">GBPUSD</option>
                <option value="USDJPY">USDJPY</option>
                <option value="AUDUSD">AUDUSD</option>
                <option value="USDCAD">USDCAD</option>
                <option value="XAUUSD">XAUUSD (Gold)</option>
                <option value="XAGUSD">XAGUSD (Silver)</option>
                <option value="BTCUSD">BTCUSD (Bitcoin)</option>
                <option value="ETHUSD">ETHUSD (Ethereum)</option>
                <option value="EURJPY">EURJPY</option>
                <option value="GBPJPY">GBPJPY</option>
              </select>
              {errors.symbol && <p id="symbol-error" className="text-red-400 text-xs mt-1" role="alert">{errors.symbol}</p>}
            </div>
          </div>

          {/* Market Price Display */}
          {formData.symbol && currentQuote && (
            <div className="bg-slate-700/50 rounded-lg p-3 border border-slate-600">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-gray-300">Market Prices - {formData.symbol}</h4>
                <span className="text-xs text-gray-400">Updated: {currentQuote.time}</span>
              </div>
              <div className="grid grid-cols-3 gap-3">
                <div className="text-center">
                  <div className="text-xs text-gray-400 mb-1">BID</div>
                  <div className="text-lg font-mono text-red-400 font-semibold">
                    {formatPrice(currentQuote.bid, formData.symbol)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-gray-400 mb-1">ASK</div>
                  <div className="text-lg font-mono text-green-400 font-semibold">
                    {formatPrice(currentQuote.ask, formData.symbol)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-gray-400 mb-1">SPREAD</div>
                  <div className="text-sm font-mono text-blue-400">
                    {currentQuote.spread.toFixed(1)}
                  </div>
                </div>
              </div>
              <div className="mt-2 text-xs text-center">
                <span className="text-gray-400">
                  Pending order will execute when market reaches your specified price
                </span>
              </div>
            </div>
          )}

          {formData.symbol && !currentQuote && (
            <div className="bg-yellow-900/20 border border-yellow-800 rounded-lg p-3">
              <div className="text-yellow-400 text-sm text-center">
                ⚠️ No live price data available for {formData.symbol}
              </div>
            </div>
          )}

          {/* Trading Disabled Warning */}
          {isTradingDisabled && (
            <div className="bg-red-900/20 border border-red-800 rounded-lg p-3">
              <div className="text-red-400 text-sm text-center flex items-center justify-center space-x-2">
                <span>🚫</span>
                <span>
                  Trading is currently disabled {formData.symbol ? `for ${formData.symbol}` : 'globally'}
                </span>
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Direction</label>
              <select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="BUY">BUY</option>
                <option value="SELL">SELL</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Order Type</label>
              <select
                value={formData.orderType}
                onChange={(e) => setFormData({ ...formData, orderType: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="LIMIT">LIMIT</option>
                <option value="STOP">STOP</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Volume (lots) *</label>
              <input
                type="number"
                step="0.01"
                placeholder="1.00"
                value={formData.volume}
                onChange={(e) => setFormData({ ...formData, volume: e.target.value })}
                className={`w-full px-3 py-2 bg-slate-700 border rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.volume ? 'border-red-500' : 'border-slate-600'
                }`}
              />
              {errors.volume && <p className="text-red-400 text-xs mt-1">{errors.volume}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Price *</label>
              <input
                type="number"
                step={formData.symbol ? getSymbolStep(formData.symbol) : "0.00001"}
                placeholder="1.08500"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                className={`w-full px-3 py-2 bg-slate-700 border rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.price ? 'border-red-500' : 'border-slate-600'
                }`}
              />
              {errors.price && <p className="text-red-400 text-xs mt-1">{errors.price}</p>}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Stop Loss</label>
              <input
                type="number"
                step={formData.symbol ? getSymbolStep(formData.symbol) : "0.00001"}
                placeholder="Optional"
                value={formData.sl}
                onChange={(e) => setFormData({ ...formData, sl: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Take Profit</label>
              <input
                type="number"
                step={formData.symbol ? getSymbolStep(formData.symbol) : "0.00001"}
                placeholder="Optional"
                value={formData.tp}
                onChange={(e) => setFormData({ ...formData, tp: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Comment</label>
            <input
              type="text"
              placeholder="Optional comment"
              value={formData.comment}
              onChange={(e) => setFormData({ ...formData, comment: e.target.value })}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-4 border-t border-slate-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Placing Order...</span>
              </>
            ) : (
              <span>Place Order</span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default NewOrderModal; 