import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import BrokerLanding from "./pages/BrokerLanding";
import BrokerPayment from "./pages/BrokerPayment";
import BrokerConfiguration from "./pages/BrokerConfiguration";
import BrokerDashboard from "./pages/BrokerDashboard";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<BrokerLanding />} />
          <Route path="/demo" element={<Index />} />
          <Route path="/dealer" element={<Index />} />
          <Route path="/broker/payment" element={<BrokerPayment />} />
          <Route path="/broker/configuration" element={<BrokerConfiguration />} />
          <Route path="/broker/dashboard" element={<BrokerDashboard />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
