import React, { useMemo } from 'react';
import { useWebSocket } from '../contexts/WebSocketContext';
import { Users, AlertTriangle, Activity, DollarSign, TrendingUp } from 'lucide-react';

const DealerContextPanel = () => {
  const { traderAccounts, positions, orders } = useWebSocket();

  // Calculate analytics metrics
  const analytics = useMemo(() => {
    // Get unique user groups that the dealer manages
    const uniqueUserGroups = new Set(traderAccounts.map(account => account.userGroup));
    const managedUserGroups = uniqueUserGroups.size;

    // Total active accounts
    const totalActiveAccounts = traderAccounts.length;

    // Critical alerts (Stop Out + Margin Call)
    const criticalAccounts = traderAccounts.filter(account => 
      account.marginLevel <= account.marginCall
    );

    // Today's activities (positions + orders)
    const todayActivities = positions.length + orders.length;

    // P/L calculations
    const totalPL = positions.reduce((sum, position) => sum + position.pl, 0);
    const profitablePositions = positions.filter(pos => pos.pl > 0).length;
    const profitPercentage = positions.length > 0 ? Math.round((profitablePositions / positions.length) * 100) : 0;

    return {
      managedUserGroups,
      totalActiveAccounts,
      criticalAlerts: criticalAccounts.length,
      todayActivities,
      totalPL,
      profitPercentage
    };
  }, [traderAccounts, positions, orders]);

  const formatPL = (value: number) => {
    const absValue = Math.abs(value);
    if (absValue >= 1000) {
      return `${value >= 0 ? '+' : '-'}$${(absValue / 1000).toFixed(1)}K`;
    }
    return `${value >= 0 ? '+' : ''}$${value.toFixed(0)}`;
  };

  return (
    <div className="bg-slate-800 rounded-lg p-2 sm:p-4 border border-slate-700 w-full">
      {/* Header */}
      <div className="flex items-center space-x-2 mb-2 sm:mb-4">
        <TrendingUp className="w-4 sm:w-5 h-4 sm:h-5 text-blue-400" />
        <h2 className="text-base sm:text-lg font-semibold text-white">Dealer Analytics</h2>
      </div>
      
      {/* Section 1: User Groups & Accounts */}
      <div className="mb-2 sm:mb-4">
        <div className="flex items-center space-x-3 mb-2">
          <Users className="w-4 h-4 text-blue-400" />
          <span className="text-sm font-medium text-white">Managed User Groups: {analytics.managedUserGroups}</span>
        </div>
        <div className="ml-7">
          <span className="text-sm text-gray-400">Total Active Accounts: </span>
          <span className="text-white font-semibold">{analytics.totalActiveAccounts}</span>
        </div>
      </div>

      {/* Section Divider */}
      <div className="border-t border-slate-700 mb-2 sm:mb-4"></div>

      {/* Section 2: Performance Metrics */}
      <div className="space-y-3">
        {/* Critical Alerts */}
        <div className="flex items-center space-x-3">
          <AlertTriangle className={`w-4 h-4 ${analytics.criticalAlerts > 0 ? 'text-red-400 animate-pulse' : 'text-gray-500'}`} />
          <span className="text-sm text-gray-400">Critical:</span>
          <span className={`font-semibold px-2 py-1 rounded-full ${
            analytics.criticalAlerts > 0 
              ? 'text-red-300 bg-gradient-to-r from-red-500/30 to-red-600/20 border border-red-500/50 animate-pulse' 
              : 'text-green-400 bg-gradient-to-r from-green-500/20 to-green-600/10 border border-green-500/30'
          }`}>
            {analytics.criticalAlerts} alert{analytics.criticalAlerts !== 1 ? 's' : ''}
          </span>
        </div>

        {/* Today's Activities */}
        <div className="flex items-center space-x-3">
          <Activity className="w-4 h-4 text-yellow-400" />
          <span className="text-sm text-gray-400">Today:</span>
          <span className="text-white font-semibold">{analytics.todayActivities} activities</span>
        </div>

        {/* P/L Summary */}
        <div className="flex items-center space-x-3">
          <DollarSign className={`w-4 h-4 ${analytics.totalPL >= 0 ? 'text-green-400' : 'text-red-400'}`} />
          <span className="text-sm text-gray-400">P/L:</span>
          <div className="flex items-center space-x-2">
            <span className={`font-semibold ${analytics.totalPL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatPL(analytics.totalPL)}
            </span>
            <span className="text-xs text-gray-500">
              ({analytics.profitPercentage}% {analytics.profitPercentage >= 50 ? '↗' : '↘'})
            </span>
          </div>
        </div>
      </div>

      {/* Footer: Last Updated */}
      <div className="mt-2 sm:mt-4 pt-2 sm:pt-3 border-t border-slate-700">
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500">Updated:</span>
          <span className="text-xs text-gray-400">{new Date().toLocaleTimeString()}</span>
        </div>
      </div>
    </div>
  );
};

export default DealerContextPanel;
