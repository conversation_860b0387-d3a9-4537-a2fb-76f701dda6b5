# CAPTCHA Implementation Guide for OTX Dealer Terminal Pro

This guide provides multiple options for adding CAPTCHA verification to the login system in your OTX Dealer Terminal Pro application.

## Overview

CAPTCHA (Completely Automated Public Turing test to tell Computers and Humans Apart) adds an extra layer of security to prevent automated attacks and bot logins.

**Current Status**: The `AuthContext.tsx` has been updated to accept an optional `captchaToken` parameter in the login function.

## Option 1: Google reCAPTCHA v2 (Recommended)

### Features
- ✅ Most trusted and widely used
- ✅ Good bot detection
- ✅ Free tier available
- ✅ Dark theme support
- ❌ Requires Google services
- ❌ Data shared with Google

### Installation
```bash
npm install react-google-recaptcha
npm install @types/react-google-recaptcha --save-dev
```

### Setup
1. Go to [Google reCAPTCHA Console](https://www.google.com/recaptcha/admin)
2. Register your site with domain `localhost` for development
3. Get your Site Key and Secret Key
4. Add environment variables:
```bash
VITE_RECAPTCHA_SITE_KEY=your_site_key_here
```

### Implementation
Replace the content of `src/components/LoginPage.tsx`:

```tsx
import React, { useState, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { AlertCircle, Loader2, Shield } from 'lucide-react';
import ReCAPTCHA from 'react-google-recaptcha';

const LoginPage = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [captchaValue, setCaptchaValue] = useState<string | null>(null);
  const { login, isLoading } = useAuth();
  const recaptchaRef = useRef<ReCAPTCHA>(null);

  const RECAPTCHA_SITE_KEY = import.meta.env.VITE_RECAPTCHA_SITE_KEY;

  const handleCaptchaChange = (value: string | null) => {
    setCaptchaValue(value);
    if (error === 'Please complete the CAPTCHA verification') {
      setError('');
    }
  };

  const handleLogin = async () => {
    setError('');
    
    if (!username.trim()) {
      setError('Username is required.');
      return;
    }
    
    if (!password.trim()) {
      setError('Password is required.');
      return;
    }

    if (!captchaValue) {
      setError('Please complete the CAPTCHA verification');
      return;
    }
    
    const success = await login(username, password, captchaValue);
    if (!success) {
      setError('Invalid credentials or CAPTCHA verification failed');
      recaptchaRef.current?.reset();
      setCaptchaValue(null);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <span className="text-white font-bold text-2xl">OTX</span>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">OTX Dealer Platform</h1>
          <p className="text-gray-400">Professional Trading Terminal</p>
        </div>

        <div className="bg-slate-800 rounded-xl p-8 shadow-2xl border border-slate-700">
          <h2 className="text-xl font-semibold text-white mb-6 text-center">Dealer Login</h2>
          
          <div className="space-y-4">
            <input
              type="text"
              placeholder="Enter your username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            />
            
            <input
              type="password"
              placeholder="Enter your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            />

            {/* reCAPTCHA */}
            <div className="flex justify-center">
              <ReCAPTCHA
                ref={recaptchaRef}
                sitekey={RECAPTCHA_SITE_KEY}
                onChange={handleCaptchaChange}
                theme="dark"
                size="normal"
              />
            </div>
            
            {error && (
              <div className="flex items-center space-x-2 text-red-400 text-sm bg-red-900/30 border border-red-800 rounded-lg p-3">
                <AlertCircle className="w-4 h-4" />
                <span>{error}</span>
              </div>
            )}
            
            <button
              onClick={handleLogin}
              disabled={isLoading || !captchaValue}
              className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold py-3 px-4 rounded-lg hover:from-blue-600 hover:to-cyan-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Authenticating...</span>
                </>
              ) : (
                <>
                  <Shield className="w-4 h-4" />
                  <span>Login</span>
                </>
              )}
            </button>
          </div>
          
          <div className="mt-6 text-center text-sm text-gray-400">
            <p>Demo credentials: dealer01 / password123</p>
            <p className="mt-1 text-xs">Complete CAPTCHA verification to login</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
```

### Backend Verification (Node.js/Express example)
```javascript
// Backend endpoint to verify reCAPTCHA
app.post('/api/login', async (req, res) => {
  const { username, password, captchaToken } = req.body;
  
  // Verify CAPTCHA with Google
  const captchaResponse = await fetch('https://www.google.com/recaptcha/api/siteverify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: `secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${captchaToken}`
  });
  
  const captchaResult = await captchaResponse.json();
  
  if (!captchaResult.success) {
    return res.status(400).json({ error: 'CAPTCHA verification failed' });
  }
  
  // Proceed with username/password verification...
});
```

---

## Option 2: hCaptcha (Privacy-focused)

### Features
- ✅ Privacy-focused alternative to Google
- ✅ GDPR compliant
- ✅ Free tier available
- ✅ Dark theme support
- ✅ Used by Cloudflare, Discord
- ❌ Less widely adopted than reCAPTCHA

### Installation
```bash
npm install @hcaptcha/react-hcaptcha
```

### Setup
1. Go to [hCaptcha Dashboard](https://dashboard.hcaptcha.com/)
2. Register your site
3. Get your Site Key and Secret Key

### Implementation
Similar to reCAPTCHA but replace the CAPTCHA component:

```tsx
import HCaptcha from '@hcaptcha/react-hcaptcha';

// Replace reCAPTCHA component with:
<HCaptcha
  ref={hcaptchaRef}
  sitekey={HCAPTCHA_SITE_KEY}
  onVerify={handleCaptchaChange}
  onExpire={() => setCaptchaValue(null)}
  theme="dark"
/>
```

---

## Option 3: Custom Mathematical CAPTCHA

### Features
- ✅ No external dependencies
- ✅ Full control
- ✅ No data sharing
- ✅ Lightweight
- ❌ Less sophisticated bot detection
- ❌ May be easier to bypass

### Implementation
Add this to your login form:

```tsx
const [mathQuestion, setMathQuestion] = useState('');
const [mathAnswer, setMathAnswer] = useState('');
const [userMathAnswer, setUserMathAnswer] = useState('');

// Generate math question
const generateMathQuestion = () => {
  const num1 = Math.floor(Math.random() * 10) + 1;
  const num2 = Math.floor(Math.random() * 10) + 1;
  const operations = ['+', '-'];
  const operation = operations[Math.floor(Math.random() * operations.length)];
  
  let answer;
  if (operation === '+') {
    answer = num1 + num2;
  } else {
    answer = num1 - num2;
  }
  
  setMathQuestion(`${num1} ${operation} ${num2} = ?`);
  setMathAnswer(answer.toString());
};

// In your form:
<div className="space-y-2">
  <label className="block text-sm font-medium text-gray-400">
    Security Question: {mathQuestion}
  </label>
  <input
    type="number"
    placeholder="Enter the answer"
    value={userMathAnswer}
    onChange={(e) => setUserMathAnswer(e.target.value)}
    className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white"
  />
</div>

// In validation:
if (userMathAnswer !== mathAnswer) {
  setError('Incorrect answer to security question');
  generateMathQuestion();
  setUserMathAnswer('');
  return;
}
```

---

## Option 4: Custom Image CAPTCHA

### Features
- ✅ Visual verification
- ✅ No external dependencies
- ✅ Customizable appearance
- ❌ More complex implementation
- ❌ May have accessibility issues

### Implementation
Use HTML5 Canvas to generate distorted text images:

```tsx
const generateImageCaptcha = () => {
  const canvas = canvasRef.current;
  const ctx = canvas.getContext('2d');
  
  // Generate random text
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  const captchaText = Array.from({ length: 5 }, () => 
    chars[Math.floor(Math.random() * chars.length)]
  ).join('');
  
  // Draw with distortion, noise, colors
  // ... (see full implementation in the codebase)
};
```

---

## Security Considerations

### Rate Limiting
Implement rate limiting to prevent brute force attacks:

```tsx
// Add to AuthContext
const [loginAttempts, setLoginAttempts] = useState(0);
const [lockoutTime, setLockoutTime] = useState(null);

const login = async (username, password, captchaToken) => {
  if (lockoutTime && Date.now() < lockoutTime) {
    throw new Error('Account temporarily locked. Try again later.');
  }
  
  // ... login logic
  
  if (!success) {
    const newAttempts = loginAttempts + 1;
    setLoginAttempts(newAttempts);
    
    if (newAttempts >= 5) {
      setLockoutTime(Date.now() + 15 * 60 * 1000); // 15 minutes
      setLoginAttempts(0);
    }
  } else {
    setLoginAttempts(0);
    setLockoutTime(null);
  }
};
```

### Additional Security Headers
Add to your backend:

```javascript
app.use((req, res, next) => {
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  next();
});
```

### Environment Variables
Create `.env` file:
```bash
VITE_RECAPTCHA_SITE_KEY=your_site_key
RECAPTCHA_SECRET_KEY=your_secret_key
```

---

## Recommendation

For **OTX Dealer Terminal Pro**, I recommend:

1. **Google reCAPTCHA v2** for production (most reliable)
2. **Mathematical CAPTCHA** for development/demo (simple, no setup)
3. **hCaptcha** if privacy compliance is a priority

The current implementation already supports CAPTCHA tokens in the authentication flow, so you can choose any option and implement it according to your security requirements.

---

## Testing

Test your CAPTCHA implementation with:
- Valid credentials + valid CAPTCHA
- Valid credentials + invalid CAPTCHA
- Invalid credentials + valid CAPTCHA
- Multiple failed attempts (rate limiting)
- Different browser environments
- Mobile devices (responsive design)

Remember to test both development and production environments with appropriate domain configurations. 