// OTX Platform - Interactive JavaScript for Broker Demo
// This file handles all the dynamic functionality and page initialization

// Global state
let currentStep = 1;
let selectedInstruments = [];
let formData = {};

// Utility Functions
function showToast(message) {
    const toast = document.getElementById('toast');
    const toastMessage = document.getElementById('toast-message');
    
    if (toast && toastMessage) {
        toastMessage.textContent = message;
        toast.classList.remove('hidden');
        
        setTimeout(() => {
            toast.classList.add('hidden');
        }, 3000);
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('Copied to clipboard!');
    }).catch(() => {
        showToast('Failed to copy');
    });
}

function updatePageTitle() {
    const title = document.getElementById('page-title');
    if (title) {
        title.textContent = title.textContent.replace('OTX Platform', BrokerConfig.company.name);
    }
}

function updateBrandingElements() {
    // Update logos
    document.querySelectorAll('#header-logo, #footer-logo').forEach(logo => {
        logo.textContent = BrokerConfig.company.logo;
    });
    
    // Update company names
    document.querySelectorAll('#header-title, #footer-brand-name').forEach(element => {
        if (element) element.textContent = BrokerConfig.company.name;
    });
    
    // Update taglines
    const tagline = document.getElementById('header-tagline');
    if (tagline) tagline.textContent = BrokerConfig.company.tagline;
    
    // Update copyright
    const copyright = document.getElementById('footer-copyright');
    if (copyright) {
        copyright.textContent = `© ${BrokerConfig.company.year} ${BrokerConfig.company.name}. ${BrokerConfig.legal.copyright}`;
    }
}

// Landing Page Functions
function initializeLandingPage() {
    updatePageTitle();
    updateBrandingElements();
    
    // Update hero section
    const heroTitle = document.getElementById('hero-title-main');
    const heroHighlight = document.getElementById('hero-title-highlight');
    const heroDescription = document.getElementById('hero-description');
    const heroBadge = document.getElementById('hero-badge');
    
    if (heroTitle) heroTitle.textContent = BrokerConfig.hero.title;
    if (heroHighlight) heroHighlight.textContent = BrokerConfig.hero.titleHighlight;
    if (heroDescription) heroDescription.textContent = BrokerConfig.hero.description;
    if (heroBadge) heroBadge.textContent = BrokerConfig.hero.badge;
    
    // Update CTA buttons
    const primaryCTA = document.getElementById('hero-primary-cta');
    const secondaryCTA = document.getElementById('hero-secondary-cta');
    const headerCTA = document.getElementById('header-cta');
    const ctaButton = document.getElementById('cta-button');
    
    if (primaryCTA) primaryCTA.textContent = BrokerConfig.hero.primaryCTA;
    if (secondaryCTA) secondaryCTA.textContent = BrokerConfig.hero.secondaryCTA;
    if (headerCTA) headerCTA.textContent = 'Get Started';
    if (ctaButton) ctaButton.innerHTML = `${BrokerConfig.hero.primaryCTA} <span class="arrow">→</span>`;
    
    // Update CTA description
    const ctaDescription = document.getElementById('cta-description');
    if (ctaDescription) {
        ctaDescription.textContent = ctaDescription.textContent.replace('OTX Platform', BrokerConfig.company.name);
    }
    
    // Populate features
    populateFeatures();
    
    // Populate pricing
    populatePricing();
    
    // Add event listeners
    addLandingPageEventListeners();
}

function populateFeatures() {
    const featuresGrid = document.getElementById('features-grid');
    if (!featuresGrid) return;
    
    featuresGrid.innerHTML = '';
    
    BrokerConfig.features.forEach(feature => {
        const featureCard = document.createElement('div');
        featureCard.className = 'feature-card';
        featureCard.innerHTML = `
            <div class="feature-icon">${feature.icon}</div>
            <h3>${feature.title}</h3>
            <p>${feature.description}</p>
        `;
        featuresGrid.appendChild(featureCard);
    });
}

function populatePricing() {
    const pricingGrid = document.getElementById('pricing-grid');
    if (!pricingGrid) return;
    
    pricingGrid.innerHTML = '';
    
    BrokerConfig.plans.forEach(plan => {
        const pricingCard = document.createElement('div');
        pricingCard.className = `pricing-card ${plan.popular ? 'popular' : ''}`;
        
        const popularBadge = plan.popular ? 
            '<div class="popular-badge"><span>⭐</span> Most Popular</div>' : 
            '';
        
        const features = plan.features.map(feature => 
            `<li>✅ ${feature}</li>`
        ).join('');
        
        const buttonText = plan.name === 'Enterprise' ? 'Contact Sales' : 'Get Started';
        const buttonClass = plan.popular ? 'btn-primary' : 'btn-secondary';
        
        pricingCard.innerHTML = `
            ${popularBadge}
            <div class="plan-header">
                <h3>${plan.name}</h3>
                <div class="plan-price">
                    ${plan.price}<span class="period">${plan.period}</span>
                </div>
                <p>${plan.description}</p>
            </div>
            <ul class="features-list">
                ${features}
            </ul>
            <button class="btn ${buttonClass}" onclick="goToPayment('${plan.name}')">
                ${buttonText}
            </button>
        `;
        pricingGrid.appendChild(pricingCard);
    });
}

function addLandingPageEventListeners() {
    // Header CTA
    const headerCTA = document.getElementById('header-cta');
    if (headerCTA) {
        headerCTA.addEventListener('click', () => goToPayment());
    }
    
    // Hero CTAs  
    const primaryCTA = document.getElementById('hero-primary-cta');
    const secondaryCTA = document.getElementById('hero-secondary-cta');
    const ctaButton = document.getElementById('cta-button');
    
    if (primaryCTA) primaryCTA.addEventListener('click', () => goToPayment());
    if (secondaryCTA) secondaryCTA.addEventListener('click', () => goToDemo());
    if (ctaButton) ctaButton.addEventListener('click', () => goToPayment());
}

function goToPayment(planName = 'Professional') {
    window.location.href = `payment.html?plan=${planName}`;
}

function goToDemo() {
    // In a real implementation, this would go to the React demo
    alert('Demo would open here - this connects to your React trading terminal');
}

// Payment Page Functions
function initializePaymentPage() {
    updatePageTitle();
    updateBrandingElements();
    
    // Get selected plan
    const selectedPlan = BrokerUtils.getSelectedPlan();
    
    // Update plan details
    updatePaymentPlanDetails(selectedPlan);
    
    // Update terms
    updatePaymentTerms();
    
    // Add event listeners
    addPaymentEventListeners();
}

function updatePaymentPlanDetails(plan) {
    const planName = document.getElementById('selected-plan-name');
    const planDescription = document.getElementById('selected-plan-description');
    const planPrice = document.getElementById('selected-plan-price');
    const planPeriod = document.getElementById('selected-plan-period');
    const planFeatures = document.getElementById('plan-features');
    const subtotal = document.getElementById('subtotal');
    const totalToday = document.getElementById('total-today');
    const submitTotal = document.getElementById('submit-total');
    const popularBadge = document.getElementById('popular-badge');
    
    if (planName) planName.textContent = `${plan.name} Plan`;
    if (planDescription) planDescription.textContent = plan.description;
    if (planPrice) planPrice.textContent = plan.price;
    if (planPeriod) planPeriod.textContent = plan.period;
    if (subtotal) subtotal.textContent = plan.price;
    
    const calculatedTotal = BrokerUtils.calculateTotal(plan);
    if (totalToday) totalToday.textContent = calculatedTotal;
    if (submitTotal) submitTotal.textContent = calculatedTotal;
    
    // Show/hide popular badge
    if (popularBadge) {
        popularBadge.style.display = plan.popular ? 'block' : 'none';
    }
    
    // Populate features
    if (planFeatures) {
        planFeatures.innerHTML = plan.features.map(feature => 
            `<li>✅ ${feature}</li>`
        ).join('');
    }
    
    // Update guarantees
    const guarantees = document.getElementById('guarantees');
    if (guarantees) {
        guarantees.innerHTML = BrokerConfig.payment.guarantees.map(guarantee => 
            `<p>• ${guarantee}</p>`
        ).join('');
    }
}

function updatePaymentTerms() {
    const termsSection = document.getElementById('terms-section');
    if (termsSection) {
        termsSection.innerHTML = `
            <p>${BrokerConfig.legal.termsConditions}</p>
            <p>${BrokerConfig.legal.autoRenewal}</p>
        `;
    }
    
    // Update SSL text
    const sslText = document.getElementById('ssl-text');
    if (sslText) sslText.textContent = BrokerConfig.payment.security;
    
    // Update header subtitle
    const headerSubtitle = document.getElementById('header-subtitle');
    if (headerSubtitle) {
        headerSubtitle.textContent = `Secure checkout powered by ${BrokerConfig.company.name}`;
    }
}

function addPaymentEventListeners() {
    // Back button
    const backBtn = document.getElementById('back-btn');
    if (backBtn) {
        backBtn.addEventListener('click', () => {
            window.location.href = 'index.html';
        });
    }
    
    // Form submission
    const paymentForm = document.getElementById('payment-form');
    if (paymentForm) {
        paymentForm.addEventListener('submit', handlePaymentSubmit);
    }
}

function handlePaymentSubmit(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('submit-btn');
    const btnContent = submitBtn.querySelector('.btn-content');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    
    // Show loading state
    btnContent.classList.add('hidden');
    btnLoading.classList.remove('hidden');
    submitBtn.disabled = true;
    
    // Collect form data
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData.entries());
    
    // Store data for next step
    const selectedPlan = BrokerUtils.getSelectedPlan();
    const brokerData = {
        billingInfo: data,
        selectedPlan: selectedPlan,
        paymentDate: new Date().toISOString(),
        subscriptionId: BrokerUtils.generateSubId()
    };
    
    localStorage.setItem('brokerOnboarding', JSON.stringify(brokerData));
    
    // Simulate payment processing
    setTimeout(() => {
        window.location.href = 'configuration.html';
    }, 3000);
}

// Configuration Page Functions
function initializeConfigurationPage() {
    updatePageTitle();
    updateBrandingElements();
    
    // Populate form options
    populateConfigurationOptions();
    
    // Initialize step 1
    updateConfigurationProgress();
    
    // Add event listeners
    addConfigurationEventListeners();
}

function populateConfigurationOptions() {
    // User groups
    const userGroupsSelect = document.getElementById('userGroups');
    if (userGroupsSelect) {
        userGroupsSelect.innerHTML = BrokerConfig.configuration.userGroups.map(option => 
            `<option value="${option.value}">${option.label}</option>`
        ).join('');
    }
    
    // Trader accounts
    const traderAccountsSelect = document.getElementById('traderAccounts');
    if (traderAccountsSelect) {
        traderAccountsSelect.innerHTML = BrokerConfig.configuration.traderAccounts.map(option => 
            `<option value="${option.value}">${option.label}</option>`
        ).join('');
    }
    
    // Instruments
    const instrumentsGrid = document.getElementById('instruments-grid');
    if (instrumentsGrid) {
        instrumentsGrid.innerHTML = BrokerConfig.configuration.instruments.map(instrument => {
            const popularBadge = instrument.popular ? 
                '<div class="popular-badge">Popular</div>' : '';
            
            return `
                <div class="instrument-card" onclick="toggleInstrument('${instrument.id}')">
                    <div class="card-header">
                        <div class="instrument-icon">${instrument.icon}</div>
                        ${popularBadge}
                    </div>
                    <h3>${instrument.title}</h3>
                    <p>${instrument.description}</p>
                    <input type="checkbox" id="${instrument.id}" name="instruments" value="${instrument.id}">
                </div>
            `;
        }).join('');
    }
}

function updateConfigurationProgress() {
    const progressFill = document.getElementById('progress-fill');
    const progressBadge = document.getElementById('progress-badge');
    
    const progress = (currentStep / 3) * 100;
    
    if (progressFill) progressFill.style.width = `${progress}%`;
    if (progressBadge) progressBadge.textContent = `Step ${currentStep} of 3`;
    
    // Update step indicators
    for (let i = 1; i <= 3; i++) {
        const indicator = document.getElementById(`step-indicator-${i}`);
        if (indicator) {
            indicator.classList.remove('active', 'completed');
            if (i < currentStep) {
                indicator.classList.add('completed');
            } else if (i === currentStep) {
                indicator.classList.add('active');
            }
        }
    }
    
    // Show current step content
    for (let i = 1; i <= 3; i++) {
        const stepContent = document.getElementById(`step-${i}`);
        if (stepContent) {
            stepContent.classList.toggle('active', i === currentStep);
        }
    }
    
    // Update navigation buttons
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const submitBtn = document.getElementById('submit-btn');
    
    if (prevBtn) {
        prevBtn.classList.toggle('hidden', currentStep === 1);
    }
    
    if (nextBtn) {
        nextBtn.classList.toggle('hidden', currentStep === 3);
    }
    
    if (submitBtn) {
        submitBtn.classList.toggle('hidden', currentStep !== 3);
    }
}

function addConfigurationEventListeners() {
    // Navigation buttons
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const submitBtn = document.getElementById('submit-btn');
    
    if (prevBtn) prevBtn.addEventListener('click', goToPreviousStep);
    if (nextBtn) nextBtn.addEventListener('click', goToNextStep);
    if (submitBtn) submitBtn.addEventListener('click', handleConfigurationSubmit);
    
    // Form inputs
    const userGroupsSelect = document.getElementById('userGroups');
    const traderAccountsSelect = document.getElementById('traderAccounts');
    
    if (userGroupsSelect) {
        userGroupsSelect.addEventListener('change', updateConfigurationSummary);
    }
    
    if (traderAccountsSelect) {
        traderAccountsSelect.addEventListener('change', updateConfigurationSummary);
    }
}

function goToPreviousStep() {
    if (currentStep > 1) {
        currentStep--;
        updateConfigurationProgress();
    }
}

function goToNextStep() {
    if (validateCurrentStep()) {
        if (currentStep < 3) {
            currentStep++;
            updateConfigurationProgress();
            
            if (currentStep === 3) {
                updateInstrumentsDescription();
            }
        }
    }
}

function validateCurrentStep() {
    if (currentStep === 1) {
        const companyName = document.getElementById('companyName').value;
        const contactEmail = document.getElementById('contactEmail').value;
        
        if (!companyName || !contactEmail) {
            alert('Please fill in required fields');
            return false;
        }
        
        // Store company info
        formData.companyName = companyName;
        formData.contactEmail = contactEmail;
    }
    
    if (currentStep === 3) {
        if (selectedInstruments.length === 0) {
            const validationMessage = document.getElementById('validation-message');
            if (validationMessage) {
                validationMessage.classList.remove('hidden');
            }
            return false;
        } else {
            const validationMessage = document.getElementById('validation-message');
            if (validationMessage) {
                validationMessage.classList.add('hidden');
            }
        }
    }
    
    return true;
}

function updateConfigurationSummary() {
    const userGroups = document.getElementById('userGroups').value;
    const traderAccounts = document.getElementById('traderAccounts').value;
    
    const summaryUserGroups = document.getElementById('summary-user-groups');
    const summaryAccounts = document.getElementById('summary-accounts');
    
    if (summaryUserGroups) summaryUserGroups.textContent = userGroups;
    if (summaryAccounts) summaryAccounts.textContent = traderAccounts;
}

function toggleInstrument(instrumentId) {
    const checkbox = document.getElementById(instrumentId);
    const card = checkbox.closest('.instrument-card');
    
    checkbox.checked = !checkbox.checked;
    card.classList.toggle('selected', checkbox.checked);
    
    if (checkbox.checked) {
        selectedInstruments.push(instrumentId);
    } else {
        selectedInstruments = selectedInstruments.filter(id => id !== instrumentId);
    }
    
    updateInstrumentsDescription();
    
    // Hide validation message if instruments are selected
    if (selectedInstruments.length > 0) {
        const validationMessage = document.getElementById('validation-message');
        if (validationMessage) {
            validationMessage.classList.add('hidden');
        }
    }
}

function updateInstrumentsDescription() {
    const description = document.getElementById('instruments-description');
    if (description) {
        const count = selectedInstruments.length;
        description.textContent = `Select the financial instruments your clients can trade (${count} selected)`;
    }
}

function handleConfigurationSubmit() {
    if (!validateCurrentStep()) return;
    
    const submitBtn = document.getElementById('submit-btn');
    const btnContent = submitBtn.querySelector('.btn-content');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    
    // Show loading state
    btnContent.classList.add('hidden');
    btnLoading.classList.remove('hidden');
    submitBtn.disabled = true;
    
    // Collect all configuration data
    const userGroups = document.getElementById('userGroups').value;
    const traderAccounts = document.getElementById('traderAccounts').value;
    
    const configData = {
        ...formData,
        userGroups,
        traderAccounts,
        selectedInstruments,
        configurationDate: new Date().toISOString()
    };
    
    // Merge with existing broker data
    const existingData = JSON.parse(localStorage.getItem('brokerOnboarding') || '{}');
    const brokerData = {
        ...existingData,
        configuration: configData
    };
    
    localStorage.setItem('brokerOnboarding', JSON.stringify(brokerData));
    
    // Simulate configuration processing
    setTimeout(() => {
        window.location.href = 'dashboard.html';
    }, 3000);
}

// Dashboard Page Functions
function initializeDashboardPage() {
    updatePageTitle();
    updateBrandingElements();
    
    // Load broker data
    const brokerData = JSON.parse(localStorage.getItem('brokerOnboarding') || '{}');
    
    // Update dashboard with company data
    updateDashboardContent(brokerData);
    
    // Populate quick start guide
    populateQuickStartGuide();
    
    // Add event listeners
    addDashboardEventListeners();
}

function updateDashboardContent(brokerData) {
    // Update dashboard title
    const dashboardTitle = document.getElementById('dashboard-title');
    const companyName = brokerData.configuration?.companyName || brokerData.billingInfo?.company || 'Your Company';
    
    if (dashboardTitle) {
        dashboardTitle.textContent = `${companyName} Dashboard`;
    }
    
    // Update company information
    const companyNameSpan = document.getElementById('company-name');
    const companyEmail = document.getElementById('company-email');
    const companyPlan = document.getElementById('company-plan');
    const subscriptionId = document.getElementById('subscription-id');
    
    if (companyNameSpan) companyNameSpan.textContent = companyName;
    if (companyEmail) companyEmail.textContent = brokerData.configuration?.contactEmail || brokerData.billingInfo?.email || '<EMAIL>';
    if (companyPlan) companyPlan.textContent = brokerData.selectedPlan?.name || 'Professional';
    if (subscriptionId) subscriptionId.textContent = brokerData.subscriptionId || BrokerUtils.generateSubId();
    
    // Update platform credentials
    const cleanCompanyName = companyName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
    
    const crmUsername = document.getElementById('crm-username');
    const dealerUsername = document.getElementById('dealer-username');
    
    if (crmUsername) crmUsername.textContent = BrokerUtils.generateEmail(BrokerConfig.platforms.crm.username, cleanCompanyName);
    if (dealerUsername) dealerUsername.textContent = BrokerUtils.generateEmail(BrokerConfig.platforms.dealer.username, cleanCompanyName);
    
    // Update trading configuration
    const userGroupsCount = document.getElementById('user-groups-count');
    const demoAccountsCount = document.getElementById('demo-accounts-count');
    const symbolGroupsCount = document.getElementById('symbol-groups-count');
    
    if (userGroupsCount) userGroupsCount.textContent = brokerData.configuration?.userGroups || '1';
    if (demoAccountsCount) demoAccountsCount.textContent = brokerData.configuration?.traderAccounts || '500';
    if (symbolGroupsCount) {
        const count = brokerData.configuration?.selectedInstruments?.length || 0;
        symbolGroupsCount.textContent = `${count} groups selected`;
    }
}

function populateQuickStartGuide() {
    const quickStartList = document.getElementById('quick-start-list');
    if (!quickStartList) return;
    
    quickStartList.innerHTML = '';
    
    BrokerConfig.quickStart.forEach((step, index) => {
        const quickStartItem = document.createElement('div');
        quickStartItem.className = `quick-start-item ${step.status}`;
        
        const statusIcon = step.status === 'completed' ? '✅' : '⏳';
        const statusClass = step.status === 'completed' ? 'completed' : 'coming-soon';
        
        quickStartItem.innerHTML = `
            <div class="step-status ${statusClass}">${statusIcon}</div>
            <div class="step-info">
                <h4>${step.title}</h4>
                <p>${step.description}</p>
                <button class="btn btn-sm ${step.status === 'completed' ? 'btn-primary' : 'btn-secondary'}" 
                        ${step.status === 'coming-soon' ? 'disabled' : ''}>
                    ${step.action}
                </button>
            </div>
        `;
        
        quickStartList.appendChild(quickStartItem);
    });
}

function addDashboardEventListeners() {
    // Platform access buttons
    const openCrm = document.getElementById('open-crm');
    const openDealer = document.getElementById('open-dealer');
    
    if (openCrm) {
        openCrm.addEventListener('click', () => {
            window.open(BrokerConfig.platforms.crm.url, '_blank');
        });
    }
    
    if (openDealer) {
        openDealer.addEventListener('click', () => {
            window.open(BrokerConfig.platforms.dealer.url, '_blank');
        });
    }
    
    // Credentials toggle
    const toggleCredentials = document.getElementById('toggle-credentials');
    if (toggleCredentials) {
        toggleCredentials.addEventListener('click', () => {
            const crmCredentials = document.getElementById('crm-credentials');
            const dealerCredentials = document.getElementById('dealer-credentials');
            
            const isHidden = crmCredentials.classList.contains('hidden');
            
            crmCredentials.classList.toggle('hidden');
            dealerCredentials.classList.toggle('hidden');
            
            toggleCredentials.textContent = isHidden ? 'Hide Credentials' : 'Show Credentials';
        });
    }
    
    // Copy buttons
    document.querySelectorAll('.copy-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            const targetId = btn.getAttribute('data-copy');
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                copyToClipboard(targetElement.textContent);
            }
        });
    });
    
    // Support buttons
    const userManualBtn = document.getElementById('user-manual-btn');
    const downloadPdfBtn = document.getElementById('download-pdf-btn');
    const documentationBtn = document.getElementById('documentation-btn');
    const contactSupportBtn = document.getElementById('contact-support-btn');
    const scheduleTrainingBtn = document.getElementById('schedule-training-btn');
    
    if (userManualBtn) {
        userManualBtn.addEventListener('click', () => {
            const modal = document.getElementById('user-manual-modal');
            if (modal) modal.classList.remove('hidden');
        });
    }
    
    if (downloadPdfBtn) {
        downloadPdfBtn.addEventListener('click', () => {
            showToast('User manual download started...');
        });
    }
    
    if (documentationBtn) {
        documentationBtn.addEventListener('click', () => {
            window.open(BrokerConfig.support.documentation, '_blank');
        });
    }
    
    if (contactSupportBtn) {
        contactSupportBtn.addEventListener('click', () => {
            window.location.href = `mailto:${BrokerConfig.support.email}`;
        });
    }
    
    if (scheduleTrainingBtn) {
        scheduleTrainingBtn.addEventListener('click', () => {
            window.open(BrokerConfig.support.training, '_blank');
        });
    }
    
    // Modal close
    const closeModal = document.getElementById('close-modal');
    if (closeModal) {
        closeModal.addEventListener('click', () => {
            const modal = document.getElementById('user-manual-modal');
            if (modal) modal.classList.add('hidden');
        });
    }
    
    // Quick start actions
    document.querySelectorAll('.quick-start-item .btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const action = btn.textContent.trim();
            
            if (action === 'Open CRM') {
                window.open(BrokerConfig.platforms.crm.url, '_blank');
            } else if (action === 'Open Dealer') {
                window.open(BrokerConfig.platforms.dealer.url, '_blank');
            } else if (action === 'Customize' || action === 'Configure') {
                showToast('Coming soon!');
            }
        });
    });
}

// Global Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        const modal = document.getElementById('user-manual-modal');
        if (modal && e.target === modal) {
            modal.classList.add('hidden');
        }
    });
    
    // Handle escape key for modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('user-manual-modal');
            if (modal && !modal.classList.contains('hidden')) {
                modal.classList.add('hidden');
            }
        }
    });
});