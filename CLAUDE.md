# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**OTX Dealer Terminal Pro** is a professional fintech trading terminal built as a React single-page application. It provides real-time trading capabilities, position management, and secure authentication for financial dealers.

## Development Commands

```bash
# Development
npm run dev          # Start dev server on port 8080
npm run build        # Production build  
npm run build:dev    # Development build with dev mode
npm run lint         # Run ESLint
npm run preview      # Preview production build locally
```

## Architecture

### Core Stack
- **React 18.3.1** with TypeScript and **Vite 5.4.1**
- **shadcn/ui** component library (35+ pre-built components)
- **TanStack React Query** for server state management
- **React Router DOM** for client-side routing
- **Tailwind CSS** for styling

### State Management Pattern
- **AuthContext** (`src/contexts/AuthContext.tsx`) - Handles authentication, CAPTCHA validation, and rate limiting
- **WebSocketContext** (`src/contexts/WebSocketContext.tsx`) - Manages real-time trading data connections
- **React Query** for server state caching and synchronization

### Key Components Structure
```
src/components/
├── ui/              # shadcn/ui components (Button, Card, Input, etc.)
├── modals/          # Trading operation modals (NewOrderModal, ModifyPositionModal)
├── tabs/            # Data view tabs (OpenPositionsTab, PendingOrdersTab, etc.)
├── Dashboard.tsx    # Main trading interface
└── LoginPage.tsx    # Authentication with CAPTCHA
```

## Security Implementation

The application implements Google reCAPTCHA v2 with comprehensive security features:

- **CAPTCHA Integration**: See `CAPTCHA_IMPLEMENTATION_GUIDE.md` for implementation details
- **Rate Limiting**: Built into AuthContext with lockout mechanisms
- **Environment Configuration**: Use `env-setup-guide.md` for reCAPTCHA setup
- **Token Validation**: CAPTCHA tokens required for authentication flow

## Trading Terminal Features

### Core Functionality
- **Real-time Position Management**: Open positions with P&L tracking
- **Order Management**: Pending orders with modification capabilities  
- **Price Quotes**: Live market data display
- **Deal History**: Comprehensive trade audit logs
- **Trader Information**: Real-time trader status and activity

### Modal-Driven Workflows
Trading operations use modal dialogs for:
- Creating new orders
- Modifying existing positions
- Closing positions
- Managing pending orders

## Development Patterns

### Component Development
- Use existing shadcn/ui components from `src/components/ui/`
- Follow the modal pattern for trading operations
- Implement responsive design with Tailwind CSS utilities
- Use React Hook Form with Zod validation for forms

### Data Fetching
- Use React Query for server state management
- WebSocket connections managed through WebSocketContext
- Authentication state persisted in AuthContext

### Styling Conventions
- Tailwind CSS with custom color scheme for trading terminal
- Dark theme optimized for professional trading environments
- Responsive design with mobile-first approach

## Configuration Files

- **TypeScript**: Strict config with path aliases (`@/*` → `./src/*`)
- **ESLint**: Flat config with React Hooks and TypeScript integration
- **Vite**: React SWC plugin for fast compilation, dev server on port 8080
- **PostCSS**: Autoprefixer for cross-browser compatibility

## Environment Setup

1. Install dependencies: `npm install`
2. Configure reCAPTCHA: Follow `env-setup-guide.md`
3. Start development: `npm run dev`
4. Access application: http://localhost:8080

## Important Notes

- The codebase uses both npm and Bun lockfiles for package management flexibility
- Lovable.dev integration provides collaborative development features
- Component tagging system for development tracking
- Hot reload enabled for efficient development workflow