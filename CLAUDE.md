# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**OTX Dealer Terminal Pro** is a professional fintech trading terminal built as a React single-page application. It provides real-time trading capabilities, position management, and secure authentication for financial dealers.

## Development Commands

```bash
# Development
npm install          # Install dependencies
npm run dev          # Start dev server on port 8080
npm run build        # Production build  
npm run build:dev    # Development build with dev mode
npm run lint         # Run ESLint (flat config with React Hooks + TypeScript)
npm run preview      # Preview production build locally

# Alternative package manager
bun install          # Alternative to npm install (lockfiles for both npm/bun available)

# Testing (currently no test scripts configured - add as needed)
# npm test            # Run tests (not yet configured)
# npm run test:watch  # Run tests in watch mode (not yet configured)
```

## Architecture

### Core Stack
- **React 18.3.1** with TypeScript and **Vite 5.4.1** (React SWC plugin for fast compilation)
- **shadcn/ui** component library (40+ pre-built Radix UI components)
- **TanStack React Query v5** for server state management and caching
- **React Router DOM v6** for client-side routing
- **Tailwind CSS v3** with custom design system and dark theme
- **React Hook Form + Zod** for form validation
- **Google reCAPTCHA v2** for authentication security

### State Management Pattern
- **AuthContext** (`src/contexts/AuthContext.tsx`) - Authentication state, CAPTCHA validation, and rate limiting with loading states
- **WebSocketContext** (`src/contexts/WebSocketContext.tsx`) - Real-time trading data connections, quotes, positions, orders, and audit logging
- **React Query** for server state caching, synchronization, and background updates
- **TypeScript interfaces** (`src/types/trading.ts`) - Comprehensive type definitions for trading entities

### Real-Time Data Management
The WebSocketContext provides mock real-time data simulation with:
- Live price quote updates every 1 second with realistic volatility
- Position P&L recalculation based on current market prices
- Comprehensive audit logging for all trading operations (CREATE, EDIT, DELETE, CLOSE, CANCEL)
- Trading disable/enable controls per symbol or globally
- Mock trader account data with margin levels and equity calculations

### Key Components Structure
```
src/
├── components/
│   ├── ui/              # shadcn/ui components (Button, Card, Input, Table, SortableTable, etc.)
│   ├── modals/          # Trading operation modals (NewOrderModal, ModifyPositionModal, etc.)
│   ├── tabs/            # Data view tabs (OpenPositionsTab, PendingOrdersTab, HistoryTabs, AuditLogTab)
│   ├── Dashboard.tsx    # Main trading interface with panel layout
│   └── LoginPage.tsx    # Authentication with CAPTCHA integration
├── contexts/            # React contexts for global state management
├── types/               # TypeScript type definitions
├── utils/               # Utility functions (price formatting, etc.)
├── hooks/               # Custom React hooks (use-mobile, custom toast hooks)
└── pages/               # Router pages (BrokerDashboard, BrokerConfiguration, BrokerLanding, etc.)
```

### Component Architecture Patterns
- **Panel-based Layout**: Main Dashboard uses resizable panels for trading workspace
- **Modal-driven Operations**: All trading actions (new orders, position modifications) use modal dialogs
- **Sortable Tables**: Custom SortableTable component for data display with ARIA accessibility
- **Context Menu Integration**: Right-click context menus for trading operations
- **Real-time Updates**: Components automatically re-render with WebSocket data changes

## Security Implementation

The application implements Google reCAPTCHA v2 with comprehensive security features:

- **CAPTCHA Integration**: See `CAPTCHA_IMPLEMENTATION_GUIDE.md` for implementation details
- **Rate Limiting**: Built into AuthContext with lockout mechanisms
- **Environment Configuration**: Use `env-setup-guide.md` for reCAPTCHA setup
- **Token Validation**: CAPTCHA tokens required for authentication flow

## Trading Terminal Features

### Core Functionality
- **Real-time Position Management**: Open positions with P&L tracking
- **Order Management**: Pending orders with modification capabilities  
- **Price Quotes**: Live market data display
- **Deal History**: Comprehensive trade audit logs
- **Trader Information**: Real-time trader status and activity

### Modal-Driven Workflows
Trading operations use modal dialogs for:
- Creating new orders
- Modifying existing positions
- Closing positions
- Managing pending orders

## Development Patterns

### Component Development
- Use existing shadcn/ui components from `src/components/ui/`
- Follow the modal pattern for trading operations
- Implement responsive design with Tailwind CSS utilities
- Use React Hook Form with Zod validation for forms
- Utilize SortableTable component for data display with built-in sorting and filtering

### Data Fetching and State Management
- Use React Query for server state management (when connecting to real APIs)
- WebSocket connections managed through WebSocketContext for real-time data
- Authentication state persisted in AuthContext with CAPTCHA integration
- All trading operations automatically generate audit log entries

### Development Workflow
- Mock data is provided through WebSocketContext for development and testing
- Real-time price simulation runs automatically when WebSocket connects
- Login credentials for demo: username `dealer01`, password `password123`
- All trading operations (create, modify, close, cancel) are fully functional with mock data

### Application Routing Structure (Production)
```
/ (root)               → Broker landing/marketing page (primary entry)
/demo                  → Trading terminal with authentication
/dealer                → Trading terminal (alias for /demo)
/broker/payment        → Payment processing integration
/broker/configuration  → Multi-step broker setup wizard
/broker/dashboard      → Broker management interface
```

**Production Flow**: Visitors land on broker marketing page → "View Demo" → Trading terminal

### Styling Conventions
- Tailwind CSS with custom color scheme for trading terminal
- Dark theme optimized for professional trading environments
- Responsive design with mobile-first approach

## Configuration Files

- **TypeScript**: Multi-project setup with `tsconfig.json` (base), `tsconfig.app.json`, `tsconfig.node.json`
  - Path aliases (`@/*` → `./src/*`) configured for clean imports
  - Relaxed strict mode settings for development flexibility (noImplicitAny: false, strictNullChecks: false)
- **ESLint**: Flat config (`eslint.config.js`) with React Hooks and TypeScript integration
  - React Refresh plugin for hot reloading in development
  - Unused variables disabled for development workflow (`@typescript-eslint/no-unused-vars: off`)
- **Vite**: React SWC plugin for fast compilation, dev server on port 8080
  - Lovable.dev component tagger integration for development mode only
  - Path alias resolution matches TypeScript configuration
- **Tailwind CSS**: Custom design system with CSS variables and dark mode support
- **PostCSS**: Autoprefixer for cross-browser compatibility

## Environment Setup

1. **Install dependencies**: `npm install` (or `bun install`)
2. **Configure reCAPTCHA**: Create `.env` file with development key:
   ```bash
   VITE_RECAPTCHA_SITE_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI
   ```
   *(Google's test key for development - see `env-setup-guide.md` for details)*
3. **Start development**: `npm run dev`
4. **Access application**: http://localhost:8080 (Broker landing page)
5. **Access demo directly**: http://localhost:8080/demo (Trading terminal)
6. **Login credentials**: username `dealer01`, password `password123`

## Important Notes

- The codebase uses both npm and Bun lockfiles for package management flexibility
- Lovable.dev integration provides collaborative development features
- Component tagging system for development tracking
- Hot reload enabled for efficient development workflow