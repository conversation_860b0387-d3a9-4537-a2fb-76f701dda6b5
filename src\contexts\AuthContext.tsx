import React, { createContext, useContext, useState, ReactNode } from 'react';

interface AuthContextType {
  isAuthenticated: boolean;
  userInfo: { username: string; userId: string } | null;
  login: (username: string, password: string, captchaToken?: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userInfo, setUserInfo] = useState<{ username: string; userId: string } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const login = async (username: string, password: string, captchaToken?: string): Promise<boolean> => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // In a real application, you would:
      // 1. Send username, password, and captchaToken to your backend
      // 2. Backend verifies CAPTCHA with Google's API
      // 3. Backend returns authentication result
      
      // For demo purposes, we'll simulate CAPTCHA validation
      if (!captchaToken) {
        console.log('CAPTCHA token missing');
        return false;
      }
      
      // Mock authentication - in real app, this would be an API call
      if (username === 'dealer01' && password === 'password123') {
        console.log('Login successful with CAPTCHA verification');
        setIsAuthenticated(true);
        setUserInfo({ username, userId: 'dealer01' });
        return true;
      }
      
      console.log('Invalid credentials');
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setIsAuthenticated(false);
    setUserInfo(null);
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, userInfo, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};
