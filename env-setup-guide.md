# Environment Setup for Google reCAPTCHA

## Quick Setup for Development

Create a `.env` file in your project root with the following content:

```bash
# Google reCAPTCHA Configuration
VITE_RECAPTCHA_SITE_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI
```

**Note**: This is Google's test key that always passes validation - perfect for development!

## Manual Setup Steps

1. **Create .env file**:
   - Right-click in your project root folder
   - Create new file named `.env` (exactly, no extension)
   - Add the line above

2. **Alternative using PowerShell**:
   ```powershell
   Set-Content -Path ".env" -Value "VITE_RECAPTCHA_SITE_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"
   ```

3. **Alternative using Command Prompt**:
   ```cmd
   echo VITE_RECAPTCHA_SITE_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI > .env
   ```

## Production Setup

For production deployment:

1. Go to [Google reCAPTCHA Console](https://www.google.com/recaptcha/admin)
2. Register your site with your actual domain
3. Get your production Site Key
4. Replace the test key with your production key in `.env`

## Testing the Implementation

1. **Start the development server**: `npm run dev`
2. **Open your browser** to `http://localhost:5173`
3. **You should see**:
   - Login form with Google reCAPTCHA widget
   - Dark theme CAPTCHA (matches your app design)
   - "I'm not a robot" checkbox

4. **Test the flow**:
   - Enter credentials: `dealer01` / `password123`
   - Complete the CAPTCHA verification
   - Click "Login" - should work successfully!

## Current Implementation Features

✅ **Google reCAPTCHA v2** integrated
✅ **Dark theme** to match your app design
✅ **Test keys** for immediate development testing
✅ **Error handling** for incomplete CAPTCHA
✅ **Automatic reset** on failed login attempts
✅ **Visual feedback** with Shield icon and proper messaging
✅ **Responsive design** works on mobile devices
✅ **TypeScript support** with full type safety

## Security Features

- **CAPTCHA validation** required before login
- **Automatic token reset** on failed attempts
- **Client-side validation** for better UX
- **Server-side token verification** ready (in AuthContext)
- **Production-ready** architecture

Your reCAPTCHA implementation is now fully functional and ready for testing! 