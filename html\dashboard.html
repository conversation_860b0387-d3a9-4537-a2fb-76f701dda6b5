<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">Company Dashboard - OTX Platform</title>
    <link rel="stylesheet" href="assets/styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="logo" id="header-logo">OTX</div>
                <div class="header-info">
                    <h1 id="dashboard-title">Company Dashboard</h1>
                    <p>Your trading platform is ready!</p>
                </div>
            </div>
            <div class="status-badge platform-active">
                ✅ Platform Active
            </div>
        </div>
    </header>

    <div class="dashboard-container">
        <!-- Welcome Section -->
        <section class="welcome-section">
            <div class="welcome-content">
                <h2>🎉 Welcome to Your Trading Platform!</h2>
                <p>Congratulations! Your complete trading infrastructure is now live and ready for business.</p>
            </div>
        </section>

        <div class="dashboard-grid">
            <!-- Company Information -->
            <div class="dashboard-card">
                <h3 class="card-title">🏢 Company Information</h3>
                <div class="info-grid" id="company-info">
                    <div class="info-item">
                        <label>Company Name:</label>
                        <span id="company-name">Your Company</span>
                    </div>
                    <div class="info-item">
                        <label>Contact Email:</label>
                        <span id="company-email"><EMAIL></span>
                    </div>
                    <div class="info-item">
                        <label>Plan:</label>
                        <span id="company-plan">Professional</span>
                    </div>
                    <div class="info-item">
                        <label>Subscription ID:</label>
                        <span id="subscription-id">SUB_1234567890</span>
                    </div>
                </div>
            </div>

            <!-- Platform Access -->
            <div class="dashboard-card">
                <h3 class="card-title">🚀 Platform Access</h3>
                
                <!-- CRM Admin Portal -->
                <div class="platform-item">
                    <div class="platform-header">
                        <div class="platform-info">
                            <h4 id="crm-title">CRM Admin Portal</h4>
                            <p id="crm-description">Manage clients, leads, and business operations</p>
                        </div>
                        <button class="btn btn-primary btn-sm" id="open-crm">Open CRM</button>
                    </div>
                    <div class="credentials hidden" id="crm-credentials">
                        <div class="credential-item">
                            <label>Username:</label>
                            <span id="crm-username"><EMAIL></span>
                            <button class="copy-btn" data-copy="crm-username">📋</button>
                        </div>
                        <div class="credential-item">
                            <label>Password:</label>
                            <span id="crm-password">Demo123!</span>
                            <button class="copy-btn" data-copy="crm-password">📋</button>
                        </div>
                    </div>
                </div>

                <!-- Dealer Terminal -->
                <div class="platform-item">
                    <div class="platform-header">
                        <div class="platform-info">
                            <h4 id="dealer-title">Dealer Terminal</h4>
                            <p id="dealer-description">Professional trading and risk management</p>
                        </div>
                        <button class="btn btn-primary btn-sm" id="open-dealer">Open Dealer</button>
                    </div>
                    <div class="credentials hidden" id="dealer-credentials">
                        <div class="credential-item">
                            <label>Username:</label>
                            <span id="dealer-username"><EMAIL></span>
                            <button class="copy-btn" data-copy="dealer-username">📋</button>
                        </div>
                        <div class="credential-item">
                            <label>Password:</label>
                            <span id="dealer-password">Dealer123!</span>
                            <button class="copy-btn" data-copy="dealer-password">📋</button>
                        </div>
                    </div>
                </div>

                <div class="credentials-toggle">
                    <button class="btn btn-ghost" id="toggle-credentials">Show Credentials</button>
                </div>
            </div>

            <!-- Quick Start Guide -->
            <div class="dashboard-card">
                <h3 class="card-title">🚀 Quick Start Guide</h3>
                <p class="card-description">Complete these steps to get your brokerage fully operational</p>
                
                <div class="quick-start-list" id="quick-start-list">
                    <!-- Quick start items will be populated by JavaScript -->
                </div>
            </div>

            <!-- Trading Configuration -->
            <div class="dashboard-card">
                <h3 class="card-title">⚙️ Trading Configuration</h3>
                <div class="config-grid" id="trading-config">
                    <div class="config-item">
                        <label>User Groups:</label>
                        <span id="user-groups-count">1</span>
                    </div>
                    <div class="config-item">
                        <label>Demo Accounts:</label>
                        <span id="demo-accounts-count">500</span>
                    </div>
                    <div class="config-item">
                        <label>Symbol Groups:</label>
                        <span id="symbol-groups-count">0 groups selected</span>
                    </div>
                </div>
            </div>

            <!-- Setup Progress -->
            <div class="dashboard-card">
                <h3 class="card-title">📊 Platform Setup Progress</h3>
                <p class="card-description">Your platform configuration is complete. You can now access all components.</p>
                
                <div class="progress-bar-container">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%"></div>
                    </div>
                    <div class="progress-text">100% Complete</div>
                </div>
            </div>

            <!-- Support & Resources -->
            <div class="dashboard-card">
                <h3 class="card-title">🎧 Support & Resources</h3>
                
                <div class="support-buttons">
                    <button class="btn btn-secondary btn-sm" id="user-manual-btn">User Manual</button>
                    <button class="btn btn-secondary btn-sm" id="download-pdf-btn">Download PDF</button>
                    <button class="btn btn-secondary btn-sm" id="documentation-btn">Documentation</button>
                    <button class="btn btn-secondary btn-sm" id="contact-support-btn">Contact Support</button>
                    <button class="btn btn-secondary btn-sm" id="schedule-training-btn">Schedule Training</button>
                </div>
            </div>
        </div>
    </div>

    <!-- User Manual Modal -->
    <div class="modal hidden" id="user-manual-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>OTX Dealer Platform User Manual</h3>
                <button class="close-btn" id="close-modal">×</button>
            </div>
            <div class="modal-body">
                <div class="manual-section">
                    <h4>Getting Started</h4>
                    <p><strong>Version:</strong> 1.0 - Publication Date: 2025-07-30</p>
                    
                    <h5>Demo Credentials:</h5>
                    <ul>
                        <li><strong>Username:</strong> dealer01</li>
                        <li><strong>Password:</strong> password123</li>
                    </ul>

                    <h5>System Requirements:</h5>
                    <ul>
                        <li>Chrome 90+, Firefox 88+, Safari 14+, Edge 90+</li>
                        <li>8GB RAM minimum</li>
                        <li>1920x1080 resolution</li>
                        <li>10Mbps internet connection</li>
                    </ul>

                    <h5>Dashboard Overview:</h5>
                    <p>The platform features a 4-panel layout:</p>
                    <ul>
                        <li><strong>Dealer Analytics Panel:</strong> Real-time performance metrics</li>
                        <li><strong>Trader Info Panel:</strong> Client account information</li>
                        <li><strong>Price Quotes Panel:</strong> Live market data</li>
                        <li><strong>Main Data Panel:</strong> Trading operations and history</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div class="toast hidden" id="toast">
        <span id="toast-message">Copied to clipboard!</span>
    </div>

    <!-- Scripts -->
    <script src="assets/config.js"></script>
    <script src="assets/script.js"></script>
    <script>
        // Initialize the dashboard page
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboardPage();
        });
    </script>
</body>
</html>