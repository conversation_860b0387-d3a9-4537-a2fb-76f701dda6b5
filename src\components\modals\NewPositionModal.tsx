import React, { useState, useEffect } from 'react';
import { X, Loader2 } from 'lucide-react';
import { useWebSocket } from '../../contexts/WebSocketContext';
import { formatPrice, getSymbolPrecision, getSymbolStep } from '../../utils/priceFormatting';

interface NewPositionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const NewPositionModal = ({ isOpen, onClose }: NewPositionModalProps) => {
  const { quotes, tradingDisabledSymbols, allTradingDisabled, addPosition, addAuditEntry } = useWebSocket();
  const [formData, setFormData] = useState({
    clientAccount: '',
    symbol: '',
    type: 'BUY',
    volume: '',
    sl: '',
    tp: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // ESC key handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  // Find current quote for selected symbol
  const currentQuote = quotes.find(q => q.symbol === formData.symbol);
  
  // Check if trading is disabled for this symbol
  const isTradingDisabled = allTradingDisabled || (formData.symbol && tradingDisabledSymbols.has(formData.symbol));

  const handleSubmit = async () => {
    setErrors({});
    
    // Check for trading restrictions first
    if (isTradingDisabled) {
      setErrors({ general: 'Trading is currently disabled for this symbol' });
      return;
    }
    
    // Validation
    const newErrors: Record<string, string> = {};
    if (!formData.clientAccount) newErrors.clientAccount = 'This field is required.';
    if (!formData.symbol) newErrors.symbol = 'This field is required.';
    if (!formData.volume) newErrors.volume = 'This field is required.';
    else if (isNaN(Number(formData.volume)) || Number(formData.volume) <= 0) {
      newErrors.volume = 'Please enter a valid volume.';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Find current quote for pricing
      const currentQuote = quotes.find(q => q.symbol === formData.symbol);
      const openPrice = formData.type === 'BUY' 
        ? (currentQuote?.ask || 1.0) 
        : (currentQuote?.bid || 1.0);
      
      // Create the new position
      addPosition({
        accountId: formData.clientAccount,
        symbol: formData.symbol,
        type: formData.type,
        volume: Number(formData.volume),
        openPrice: Number(openPrice.toFixed(getSymbolPrecision(formData.symbol))),
        sl: formData.sl ? Number(formData.sl) : undefined,
        tp: formData.tp ? Number(formData.tp) : undefined,
        swap: 0,
        commission: -Number(formData.volume) * 7, // Mock commission calculation
        openTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
      });
      
      // Generate a new position ID for the audit log
      const newPositionId = `POS${Date.now().toString().slice(-6)}`;
      
      // Add audit entry for new position creation
      addAuditEntry({
        action: 'CREATE',
        entityType: 'Position',
        entityId: newPositionId,
        accountId: formData.clientAccount,
        symbol: formData.symbol,
        details: `Created new position ${newPositionId} - ${formData.symbol} ${formData.type} ${formData.volume} lots${formData.sl ? `, SL: ${formData.sl}` : ''}${formData.tp ? `, TP: ${formData.tp}` : ''}`
      });
      
      console.log('Position placed:', formData);
      onClose();
      
      // Reset form
      setFormData({
        clientAccount: '',
        symbol: '',
        type: 'BUY',
        volume: '',
        sl: '',
        tp: ''
      });
      
      // In real app, you would show a success notification here
    } catch (error) {
      setErrors({ general: 'Market is closed' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700">
          <h3 className="text-lg font-semibold text-white">New Position</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <div className="p-4 space-y-4">
          {errors.general && (
            <div className="p-3 bg-red-900/30 border border-red-800 rounded text-red-400 text-sm">
              {errors.general}
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Client Account *</label>
            <select
              value={formData.clientAccount}
              onChange={(e) => setFormData({ ...formData, clientAccount: e.target.value })}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select account...</option>
              <option value="ACC001">ACC001 - John Doe</option>
              <option value="ACC002">ACC002 - Jane Smith</option>
              <option value="ACC003">ACC003 - Mike Johnson</option>
              <option value="ACC004">ACC004 - Sarah Wilson</option>
              <option value="ACC005">ACC005 - David Brown</option>
              <option value="ACC006">ACC006 - Lisa Davis</option>
              <option value="ACC007">ACC007 - Tom Miller</option>
            </select>
            {errors.clientAccount && <p className="text-red-400 text-xs mt-1">{errors.clientAccount}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Symbol *</label>
            <select
              value={formData.symbol}
              onChange={(e) => setFormData({ ...formData, symbol: e.target.value })}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select symbol...</option>
              <option value="EURUSD">EURUSD</option>
              <option value="GBPUSD">GBPUSD</option>
              <option value="USDJPY">USDJPY</option>
              <option value="AUDUSD">AUDUSD</option>
              <option value="USDCAD">USDCAD</option>
              <option value="XAUUSD">XAUUSD (Gold)</option>
              <option value="XAGUSD">XAGUSD (Silver)</option>
              <option value="BTCUSD">BTCUSD (Bitcoin)</option>
              <option value="ETHUSD">ETHUSD (Ethereum)</option>
              <option value="EURJPY">EURJPY</option>
              <option value="GBPJPY">GBPJPY</option>
            </select>
            {errors.symbol && <p className="text-red-400 text-xs mt-1">{errors.symbol}</p>}
          </div>

          {/* Market Price Display */}
          {formData.symbol && currentQuote && (
            <div className="bg-slate-700/50 rounded-lg p-3 border border-slate-600">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-gray-300">Market Prices - {formData.symbol}</h4>
                <span className="text-xs text-gray-400">Updated: {currentQuote.time}</span>
              </div>
              <div className="grid grid-cols-3 gap-3">
                <div className="text-center">
                  <div className="text-xs text-gray-400 mb-1">BID</div>
                  <div className="text-lg font-mono text-red-400 font-semibold">
                    {formatPrice(currentQuote.bid, formData.symbol)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-gray-400 mb-1">ASK</div>
                  <div className="text-lg font-mono text-green-400 font-semibold">
                    {formatPrice(currentQuote.ask, formData.symbol)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-gray-400 mb-1">SPREAD</div>
                  <div className="text-sm font-mono text-blue-400">
                    {currentQuote.spread.toFixed(1)}
                  </div>
                </div>
              </div>
              <div className="mt-2 text-xs text-center">
                <span className="text-gray-400">
                  {formData.type === 'BUY' ? 'You will buy at ASK price' : 'You will sell at BID price'}
                </span>
              </div>
            </div>
          )}

          {formData.symbol && !currentQuote && (
            <div className="bg-yellow-900/20 border border-yellow-800 rounded-lg p-3">
              <div className="text-yellow-400 text-sm text-center">
                ⚠️ No live price data available for {formData.symbol}
              </div>
            </div>
          )}

          {/* Trading Disabled Warning */}
          {isTradingDisabled && (
            <div className="bg-red-900/20 border border-red-800 rounded-lg p-3">
              <div className="text-red-400 text-sm text-center flex items-center justify-center space-x-2">
                <span>🚫</span>
                <span>
                  Trading is currently disabled {formData.symbol ? `for ${formData.symbol}` : 'globally'}
                </span>
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Type *</label>
              <select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="BUY">Buy</option>
                <option value="SELL">Sell</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Volume *</label>
              <input
                type="number"
                step="0.01"
                min="0.01"
                placeholder="0.01"
                value={formData.volume}
                onChange={(e) => setFormData({ ...formData, volume: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {errors.volume && <p className="text-red-400 text-xs mt-1">{errors.volume}</p>}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Stop Loss</label>
              <input
                type="number"
                step={formData.symbol ? getSymbolStep(formData.symbol) : "0.00001"}
                placeholder="Optional"
                value={formData.sl}
                onChange={(e) => setFormData({ ...formData, sl: e.target.value })}
                disabled={isTradingDisabled}
                className={`w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isTradingDisabled ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Take Profit</label>
              <input
                type="number"
                step={formData.symbol ? getSymbolStep(formData.symbol) : "0.00001"}
                placeholder="Optional"
                value={formData.tp}
                onChange={(e) => setFormData({ ...formData, tp: e.target.value })}
                disabled={isTradingDisabled}
                className={`w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isTradingDisabled ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-4 border-t border-slate-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Placing...</span>
              </>
            ) : (
              <span>Place Position</span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default NewPositionModal;
