/* OTX Platform - Unified Styles for Broker Demo */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-blue: #3b82f6;
    --primary-cyan: #06b6d4;
    --primary-blue-hover: #2563eb;
    --primary-cyan-hover: #0891b2;
    
    --slate-900: #0f172a;
    --slate-800: #1e293b;
    --slate-700: #334155;
    --slate-600: #475569;
    --slate-500: #64748b;
    --slate-400: #94a3b8;
    --slate-300: #cbd5e1;
    --slate-200: #e2e8f0;
    --slate-100: #f1f5f9;
    
    --blue-900: #1e3a8a;
    --blue-800: #1e40af;
    --blue-500: #3b82f6;
    --blue-400: #60a5fa;
    
    --green-400: #4ade80;
    --green-500: #22c55e;
    --red-400: #f87171;
    --yellow-400: #facc15;
    --purple-400: #c084fc;
    --cyan-400: #22d3ee;
    
    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-7xl: 4.5rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: white;
    background: linear-gradient(135deg, var(--slate-900) 0%, var(--blue-900) 50%, var(--slate-900) 100%);
    min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    color: #d1d5db;
    font-size: var(--font-size-base);
}

/* Utility Classes */
.hidden { display: none !important; }
.gradient-text {
    background: linear-gradient(135deg, var(--blue-400) 0%, var(--cyan-400) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-center { text-align: center; }
.text-white { color: white; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }

/* Header */
.header {
    background: rgba(30, 41, 59, 0.5);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid var(--slate-700);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1280px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4) var(--spacing-6);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-cyan) 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: var(--font-size-xl);
}

.brand-info h1 {
    font-size: var(--font-size-2xl);
    color: white;
    margin: 0;
}

.brand-info p {
    font-size: var(--font-size-sm);
    color: var(--slate-400);
    margin: 0;
}

.header-info h1 {
    font-size: var(--font-size-xl);
    color: white;
    margin: 0;
}

.header-info p {
    font-size: var(--font-size-sm);
    color: var(--slate-400);
    margin: 0;
}

/* Buttons - shadcn/ui style system */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    height: 40px; /* h-10 */
    padding: 8px 16px; /* px-4 py-2 */
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s;
    cursor: pointer;
    white-space: nowrap;
}

.btn-sm {
    height: 36px; /* h-9 */
    padding: 0 12px; /* px-3 */
    font-size: var(--font-size-sm);
}

.btn-lg {
    height: 44px; /* h-11 */
    padding: 0 32px; /* px-8 */
    font-size: var(--font-size-base);
    border-radius: var(--radius-md);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-cyan) 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-blue-hover) 0%, var(--primary-cyan-hover) 100%);
    transform: translateY(-1px);
}

.btn-secondary {
    background: rgba(30, 41, 59, 0.3); /* bg-slate-800/30 */
    color: white;
    border: 1px solid var(--slate-400);
}

.btn-secondary:hover {
    background: var(--slate-700);
    border-color: var(--slate-300);
    transform: none; /* Remove transform to match React */
}

.btn-ghost {
    background: transparent;
    color: var(--slate-400);
}

.btn-ghost:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    background: rgba(59, 130, 246, 0.2);
    color: var(--blue-400);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--spacing-6);
}

.progress-badge {
    background: rgba(34, 197, 94, 0.2);
    color: var(--green-400);
    border: 1px solid rgba(34, 197, 94, 0.3);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.ssl-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    background: rgba(34, 197, 94, 0.2);
    color: var(--green-400);
    border: 1px solid rgba(34, 197, 94, 0.3);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
}

.status-badge {
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.platform-active {
    background: rgba(34, 197, 94, 0.2);
    color: var(--green-400);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.popular-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--blue-500);
    color: white;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

/* Hero Section */
.hero {
    padding: var(--spacing-20) var(--spacing-6);
    text-align: center;
}

.hero-content {
    max-width: 1280px;
    margin: 0 auto;
}

.hero-title {
    font-size: var(--font-size-5xl); /* Default mobile size */
    font-weight: 700;
    color: white;
    margin-bottom: var(--spacing-6);
    line-height: 1.1;
}

@media (min-width: 768px) {
    .hero-title {
        font-size: var(--font-size-7xl); /* md:text-7xl */
    }
}

.hero-description {
    font-size: var(--font-size-xl);
    color: #d1d5db;
    margin-bottom: var(--spacing-8);
    max-width: 768px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: center;
}

@media (min-width: 640px) {
    .hero-buttons {
        flex-direction: row;
        justify-content: center;
    }
}

/* Sections - consistent spacing */
.features, .pricing {
    padding: 80px var(--spacing-6); /* py-20 = 80px */
}

.features {
    background: rgba(30, 41, 59, 0.3);
}

.features-content, .pricing-content {
    max-width: 1280px; /* max-w-7xl */
    margin: 0 auto;
}

.section-header {
    text-align: center;
    margin-bottom: 64px; /* mb-16 = 64px */
}

.section-header h2 {
    color: white;
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-4xl);
    font-weight: 700;
}

.section-header p {
    font-size: var(--font-size-xl);
    color: #d1d5db;
    max-width: 768px; /* max-w-3xl = 768px */
    margin: 0 auto;
}

/* Feature Grid */
.features-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.feature-card {
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid var(--slate-700);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    transition: all 0.2s;
}

.feature-card:hover {
    background: rgba(30, 41, 59, 0.7);
    transform: translateY(-2px);
}

.feature-card .feature-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-4);
}

.feature-card h3 {
    color: white;
    margin-bottom: var(--spacing-4);
}

.feature-card p {
    color: #d1d5db;
}

/* Pricing Grid */
.pricing-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .pricing-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.pricing-card {
    position: relative;
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid var(--slate-700);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    transition: all 0.2s;
}

.pricing-card.popular {
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.5) 0%, rgba(30, 41, 59, 0.5) 100%);
    border-color: var(--blue-500);
}

.pricing-card:hover {
    transform: scale(1.02); /* Reduced hover scale */
    background: rgba(30, 41, 59, 0.7); /* hover:bg-slate-800/70 */
}

.plan-header {
    text-align: center;
    margin-bottom: var(--spacing-6);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.plan-header h3 {
    color: white;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin: 0;
    display: block;
    line-height: 1.2;
    width: 100%;
    order: 1;
}

.plan-price {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: white;
    margin: 0;
    display: block;
    line-height: 1.1;
    width: 100%;
    order: 2;
}

.plan-price .period {
    font-size: var(--font-size-lg);
    color: var(--slate-400);
    font-weight: 400;
    display: inline;
}

.plan-header p {
    color: #d1d5db;
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin: 0;
    opacity: 1;
    display: block;
    width: 100%;
    order: 3;
}

.features-list {
    list-style: none;
    margin-bottom: var(--spacing-4);
    padding: 0;
}

.features-list li {
    display: block;
    width: 100%;
    color: #d1d5db;
    margin-bottom: var(--spacing-3);
    padding: var(--spacing-2) 0;
    font-size: var(--font-size-sm);
    line-height: 1.5;
    border-bottom: 1px solid rgba(71, 85, 105, 0.3);
}

.features-list li:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

/* CTA Section */
.cta {
    padding: 80px var(--spacing-6); /* py-20 */
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.5) 0%, rgba(6, 182, 212, 0.5) 100%);
}

.cta-content {
    max-width: 1024px; /* max-w-4xl */
    margin: 0 auto;
    text-align: center;
}

.cta-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-6);
}

.cta h2 {
    color: white;
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-4xl);
    font-weight: 700;
}

.cta p {
    font-size: var(--font-size-xl);
    color: #d1d5db;
    margin-bottom: var(--spacing-8);
}

/* Footer */
.footer {
    background: var(--slate-900);
    border-top: 1px solid var(--slate-700);
    padding: var(--spacing-8) var(--spacing-6);
}

.footer-content {
    max-width: 1280px;
    margin: 0 auto;
    text-align: center;
}

.footer-brand {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-4);
}

.footer-brand .logo {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-sm);
}

.footer-brand span {
    color: white;
    font-weight: 600;
}

.footer p {
    color: var(--slate-400);
}

/* Forms - matching shadcn/ui styling with reduced sizes */
.form-section {
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid var(--slate-700);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4); /* Reduced from spacing-6 */
    margin-bottom: var(--spacing-4); /* Reduced from spacing-6 */
}

.section-title {
    color: white;
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-lg); /* Increased back to readable size */
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.section-description {
    color: var(--slate-400);
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-sm); /* Increased back to readable size */
}

.form-group {
    margin-bottom: var(--spacing-3); /* Reduced from spacing-4 */
}

.form-group label {
    display: block;
    color: white;
    margin-bottom: var(--spacing-2); /* Increased back for better spacing */
    font-weight: 500;
    font-size: var(--font-size-sm); /* Increased back to readable size */
}

.form-group input,
.form-group select {
    width: 100%;
    height: 40px; /* Back to standard height */
    padding: 8px 12px; /* Back to comfortable padding */
    background: var(--slate-700);
    border: 1px solid var(--slate-600);
    border-radius: var(--radius-md);
    color: white;
    font-size: var(--font-size-sm); /* Increased back to readable size */
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--blue-500);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.form-group small {
    color: var(--slate-400);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-1);
    display: block;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
}

@media (min-width: 768px) {
    .form-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

.form-row-3 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
}

@media (min-width: 768px) {
    .form-row-3 {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* File Upload */
.file-upload {
    position: relative;
}

.upload-area {
    border: 2px dashed var(--slate-600);
    border-radius: var(--radius-md);
    padding: var(--spacing-8);
    text-align: center;
    transition: all 0.2s;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--blue-500);
    background: rgba(59, 130, 246, 0.05);
}

.upload-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-2);
}

.upload-area p {
    color: white;
    margin-bottom: var(--spacing-1);
}

.upload-area small {
    color: var(--slate-400);
}

/* Payment Page Specific - reduced sizing */
.payment-container {
    max-width: 1536px;
    margin: 0 auto;
    padding: var(--spacing-4) var(--spacing-4); /* Reduced from spacing-8 spacing-6 */
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-4); /* Reduced from spacing-8 */
}

@media (min-width: 1024px) {
    .payment-container {
        grid-template-columns: 1fr 2fr;
        gap: var(--spacing-6);
    }
}

.order-summary {
    position: sticky;
    top: var(--spacing-4); /* Reduced from spacing-8 */
    height: fit-content;
}

.summary-card {
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid var(--slate-700);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4); /* Reduced from spacing-6 */
}

.card-title {
    color: white;
    margin-bottom: var(--spacing-4); /* Reduced from spacing-6 */
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-base); /* Reduced from default */
}

.plan-details {
    background: rgba(51, 65, 85, 0.5);
    border-radius: var(--radius-lg);
    padding: var(--spacing-3); /* Reduced from spacing-4 */
    margin-bottom: var(--spacing-3); /* Reduced from spacing-4 */
}

.plan-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-2);
}

.plan-info h3 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--spacing-1);
    font-size: var(--font-size-base); /* Increased from font-size-sm */
}

.plan-info p {
    color: var(--slate-400);
    font-size: var(--font-size-sm); /* Increased from font-size-xs */
}

.pricing-breakdown {
    margin: var(--spacing-3) 0; /* Reduced from spacing-4 */
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2); /* Increased back for spacing */
    color: #d1d5db;
    font-size: var(--font-size-sm); /* Increased from font-size-xs */
}

.price-row.total {
    font-weight: 700;
    font-size: var(--font-size-base); /* Increased from font-size-sm */
    color: white;
    padding-top: var(--spacing-2);
}

.divider {
    border: none;
    border-top: 1px solid var(--slate-600);
    margin: var(--spacing-3) 0;
}

.free {
    color: var(--green-400);
}

.discount {
    color: var(--green-400);
}

.features-section {
    margin: var(--spacing-3) 0; /* Reduced spacing */
}

.features-section h4 {
    color: white;
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-sm); /* Increased from font-size-xs */
    font-weight: 500;
}

.guarantees {
    color: var(--slate-400);
    font-size: var(--font-size-xs); /* Readable size */
    margin-top: var(--spacing-3);
}

.guarantees p {
    margin-bottom: var(--spacing-1);
}

.submit-section {
    display: flex;
    justify-content: center;
    margin: var(--spacing-4) 0; /* Reduced from spacing-8 */
}

.submit-btn {
    width: 100%;
    position: relative;
    height: 44px; /* Back to comfortable height */
    font-size: var(--font-size-base); /* Readable font size */
}

@media (min-width: 768px) {
    .submit-btn {
        width: auto;
        min-width: 280px; /* Reasonable width */
    }
}

.btn-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.terms {
    text-align: center;
    color: var(--slate-400);
    font-size: var(--font-size-xs); /* Keep this size as it's fine for terms */
    margin-top: var(--spacing-4); /* Slightly more margin */
}

.terms p {
    margin-bottom: var(--spacing-1); /* Better spacing */
}

/* Configuration Page */
.configuration-container {
    max-width: 1024px;
    margin: 0 auto;
    padding: var(--spacing-8) var(--spacing-6);
}

.progress-section {
    margin-bottom: var(--spacing-8);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--slate-700);
    border-radius: var(--radius-xl);
    overflow: hidden;
    margin-bottom: var(--spacing-6);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-cyan) 100%);
    transition: width 0.3s ease;
}

.steps-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--slate-400);
    font-size: var(--font-size-sm);
}

.step.active {
    color: var(--blue-400);
}

.step.completed {
    color: var(--green-400);
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--slate-700);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    border: 2px solid var(--slate-600);
    font-size: var(--font-size-sm);
}

.step.active .step-number {
    background: var(--blue-500);
    border-color: var(--blue-400);
    color: white;
}

.step.completed .step-number {
    background: var(--green-500);
    border-color: var(--green-400);
    color: white;
}

.step-content {
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid var(--slate-700);
    border-radius: var(--radius-lg);
    padding: var(--spacing-8);
    margin-bottom: var(--spacing-8);
    display: none;
}

.step-content.active {
    display: block;
}

.step-header {
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.step-icon {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--spacing-4);
}

.step-header h2 {
    color: white;
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-3xl);
}

.step-header p {
    color: var(--slate-400);
    font-size: var(--font-size-base);
}

.configuration-form {
    max-width: 600px;
    margin: 0 auto;
}

.configuration-summary {
    background: rgba(51, 65, 85, 0.5);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    margin-top: var(--spacing-6);
}

.configuration-summary h3 {
    color: white;
    margin-bottom: var(--spacing-4);
}

.configuration-summary ul {
    list-style: none;
}

.configuration-summary li {
    color: #d1d5db;
    margin-bottom: var(--spacing-2);
    padding-left: var(--spacing-2);
}

.instruments-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-6);
}

@media (min-width: 768px) {
    .instruments-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .instruments-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.instrument-card {
    background: rgba(51, 65, 85, 0.5);
    border: 2px solid var(--slate-600);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.instrument-card:hover {
    border-color: var(--blue-500);
    background: rgba(59, 130, 246, 0.1);
}

.instrument-card.selected {
    border-color: var(--blue-500);
    background: rgba(59, 130, 246, 0.2);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-4);
}

.instrument-icon {
    font-size: var(--font-size-3xl);
}

.instrument-card h3 {
    color: white;
    margin-bottom: var(--spacing-2);
}

.instrument-card p {
    color: var(--slate-400);
    font-size: var(--font-size-sm);
}

.instrument-card input[type="checkbox"] {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    width: 20px;
    height: 20px;
}

.validation-message {
    background: rgba(248, 113, 113, 0.2);
    color: var(--red-400);
    border: 1px solid rgba(248, 113, 113, 0.3);
    border-radius: var(--radius-md);
    padding: var(--spacing-3);
    text-align: center;
    font-size: var(--font-size-sm);
}

.navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-4);
}

/* Dashboard Page */
.dashboard-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: var(--spacing-8) var(--spacing-6);
}

.welcome-section {
    text-align: center;
    margin-bottom: var(--spacing-12);
}

.welcome-content h2 {
    color: white;
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-4xl);
    font-weight: 700;
}

.welcome-content p {
    font-size: var(--font-size-xl);
    color: #d1d5db;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
}

@media (min-width: 768px) {
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.dashboard-card {
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid var(--slate-700);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
}

.dashboard-card .card-title {
    color: white;
    margin-bottom: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.card-description {
    color: var(--slate-400);
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-sm);
}

.info-grid, .config-grid {
    display: grid;
    gap: var(--spacing-3);
}

.info-item, .config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2) 0;
}

.info-item label, .config-item label {
    color: var(--slate-400);
    font-weight: 500;
}

.info-item span, .config-item span {
    color: white;
}

.platform-item {
    border: 1px solid var(--slate-600);
    border-radius: var(--radius-md);
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-4);
}

.platform-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-3);
}

.platform-info h4 {
    color: white;
    margin-bottom: var(--spacing-1);
}

.platform-info p {
    color: var(--slate-400);
    font-size: var(--font-size-sm);
}

.credentials {
    background: rgba(51, 65, 85, 0.5);
    border-radius: var(--radius-md);
    padding: var(--spacing-4);
    margin-top: var(--spacing-3);
}

.credential-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-2);
}

.credential-item label {
    color: var(--slate-400);
    min-width: 80px;
}

.credential-item span {
    color: white;
    font-family: monospace;
    flex: 1;
}

.copy-btn {
    background: none;
    border: none;
    color: var(--slate-400);
    cursor: pointer;
    padding: var(--spacing-1);
    font-size: var(--font-size-sm);
}

.copy-btn:hover {
    color: var(--blue-400);
}

.credentials-toggle {
    text-align: center;
    margin-top: var(--spacing-4);
}

.quick-start-list {
    display: grid;
    gap: var(--spacing-3);
}

.quick-start-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    border: 1px solid var(--slate-600);
    border-radius: var(--radius-md);
}

.quick-start-item.completed {
    border-color: var(--green-500);
    background: rgba(34, 197, 94, 0.1);
}

.quick-start-item.coming-soon {
    opacity: 0.6;
}

.step-status {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.step-status.completed {
    background: var(--green-500);
    color: white;
}

.step-status.coming-soon {
    background: var(--slate-600);
    color: var(--slate-400);
}

.step-info {
    flex: 1;
}

.step-info h4 {
    color: white;
    margin-bottom: var(--spacing-1);
}

.step-info p {
    color: var(--slate-400);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-2);
}

.progress-bar-container {
    text-align: center;
}

.progress-text {
    color: white;
    font-weight: 600;
    margin-top: var(--spacing-2);
}

.support-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-3);
}

@media (min-width: 768px) {
    .support-buttons {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--spacing-4);
}

.modal-content {
    background: var(--slate-800);
    border: 1px solid var(--slate-700);
    border-radius: var(--radius-lg);
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--slate-700);
}

.modal-header h3 {
    color: white;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: var(--slate-400);
    font-size: var(--font-size-2xl);
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: white;
}

.modal-body {
    padding: var(--spacing-6);
}

.manual-section h4 {
    color: white;
    margin-bottom: var(--spacing-3);
}

.manual-section h5 {
    color: var(--blue-400);
    margin: var(--spacing-4) 0 var(--spacing-2) 0;
}

.manual-section p {
    color: #d1d5db;
    margin-bottom: var(--spacing-3);
}

.manual-section ul {
    color: #d1d5db;
    margin-bottom: var(--spacing-4);
    padding-left: var(--spacing-6);
}

.manual-section ul li {
    margin-bottom: var(--spacing-1);
}

/* Toast */
.toast {
    position: fixed;
    bottom: var(--spacing-6);
    right: var(--spacing-6);
    background: var(--slate-800);
    color: white;
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--radius-md);
    border: 1px solid var(--slate-700);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 767px) {
    .hero-title {
        font-size: var(--font-size-4xl); /* Smaller on mobile */
    }
    
    .hero-description {
        font-size: var(--font-size-lg);
    }
    
    .section-header h2 {
        font-size: var(--font-size-3xl);
    }
    
    .section-header p {
        font-size: var(--font-size-lg);
    }
    
    .header-content {
        padding: var(--spacing-3) var(--spacing-4);
    }
    
    .logo {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-lg);
    }
    
    .brand-info h1 {
        font-size: var(--font-size-xl);
    }
    
    .btn-lg {
        height: 40px; /* Same as regular button on mobile */
        padding: 0 var(--spacing-6);
        font-size: var(--font-size-sm);
    }
    
    .welcome-content h2 {
        font-size: var(--font-size-3xl);
    }
    
    .step-header h2 {
        font-size: var(--font-size-2xl);
    }
    
    /* Payment page mobile adjustments */
    .payment-container {
        padding: var(--spacing-2) var(--spacing-3); /* Even smaller on mobile */
        gap: var(--spacing-3);
    }
    
    .form-section {
        padding: var(--spacing-3); /* Smaller padding on mobile */
    }
    
    .section-title {
        font-size: var(--font-size-sm); /* Smaller titles on mobile */
    }
    
    .submit-btn {
        height: 36px; /* Smaller button on mobile */
        font-size: var(--font-size-xs);
    }
}