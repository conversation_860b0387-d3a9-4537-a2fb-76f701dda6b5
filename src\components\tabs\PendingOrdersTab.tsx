import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useWebSocket } from '../../contexts/WebSocketContext';
import { Plus, Edit, X, Copy, Eye } from 'lucide-react';
import ModifyOrderModal from '../modals/ModifyOrderModal';
import NewOrderModal from '../modals/NewOrderModal';
import { formatPrice, formatPriceOrFallback } from '../../utils/priceFormatting';

const PendingOrdersTab = () => {
  const { orders, cancelOrder, traderAccounts } = useWebSocket();
  const [showNewOrder, setShowNewOrder] = useState(false);
  const [modifyingOrder, setModifyingOrder] = useState<any>(null);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    order: any;
  } | null>(null);
  const [filters, setFilters] = useState({
    userGroup: 'all',
    accountId: '',
    symbol: ''
  });

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Escape to close context menu
      if (event.key === 'Escape') {
        setContextMenu(null);
      }
    };

    const handleClickOutside = () => {
      setContextMenu(null);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('click', handleClickOutside);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // Right-click context menu handler
  const handleContextMenu = useCallback((event: React.MouseEvent, order: any) => {
    event.preventDefault();
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      order
    });
  }, []);

  // Double-click handler for quick modify
  const handleDoubleClick = useCallback((order: any) => {
    setModifyingOrder(order);
  }, []);

  // Context menu actions
  const handleContextMenuAction = (action: string, order: any) => {
    setContextMenu(null);
    
    switch (action) {
      case 'modify':
        setModifyingOrder(order);
        break;
      case 'cancel':
        handleCancelOrder(order.orderId);
        break;
      case 'copy':
        navigator.clipboard.writeText(`${order.symbol} ${order.type} ${order.volume} lots at ${order.orderPrice}`);
        break;
      case 'details':
        console.log('Show details for order:', order.orderId);
        break;
    }
  };

  // Filter orders based on current filters
  const filteredOrders = useMemo(() => {
    return orders.filter(order => {
      // User Group filter - look up the actual user group from traderAccounts
      if (filters.userGroup !== 'all') {
        const account = traderAccounts.find(acc => acc.accountId === order.accountId);
        if (account) {
          if (filters.userGroup === 'premium' && account.userGroup !== 'Premium Clients') return false;
          if (filters.userGroup === 'standard' && account.userGroup !== 'Standard Clients') return false;
        }
      }

      // Account ID filter
      if (filters.accountId && !order.accountId.toLowerCase().includes(filters.accountId.toLowerCase())) {
        return false;
      }

      // Symbol filter
      if (filters.symbol && !order.symbol.toLowerCase().includes(filters.symbol.toLowerCase())) {
        return false;
      }

      return true;
    });
  }, [orders, filters, traderAccounts]);

  const handleCancelOrder = async (orderId: string) => {
    if (confirm('Are you sure you want to cancel this order?')) {
      cancelOrder(orderId);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header with Filters and New Order Button */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-3 flex-shrink-0 mb-4">
        {/* Title and New Order Button */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-white">Pending Orders</h3>
          <button 
            onClick={() => setShowNewOrder(true)}
            className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors lg:ml-4"
            title="New Order"
          >
            <Plus className="w-4 h-4" />
            <span className="text-sm">New Order</span>
          </button>
        </div>

        {/* Inline Filters */}
        <div className="flex flex-wrap gap-2 lg:gap-3">
          <div className="min-w-0 flex-1 lg:flex-none lg:w-32">
            <label className="block text-xs font-medium text-gray-400 mb-1">User Group</label>
            <select
              value={filters.userGroup}
              onChange={(e) => setFilters({ ...filters, userGroup: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="all">All Groups</option>
              <option value="premium">Premium Clients</option>
              <option value="standard">Standard Clients</option>
            </select>
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-40">
            <label className="block text-xs font-medium text-gray-400 mb-1">Account ID</label>
            <input
              type="text"
              placeholder="Search account..."
              value={filters.accountId}
              onChange={(e) => setFilters({ ...filters, accountId: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-32">
            <label className="block text-xs font-medium text-gray-400 mb-1">Symbol</label>
            <input
              type="text"
              placeholder="e.g. EURUSD"
              value={filters.symbol}
              onChange={(e) => setFilters({ ...filters, symbol: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Orders Table - Fill remaining height */}
      <div className="flex-1 overflow-y-auto border border-slate-600 rounded-lg">
        <table className="w-full">
          <thead className="sticky top-0 bg-slate-800 z-10">
            <tr className="border-b border-slate-600">
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Account ID</th>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Order ID</th>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Symbol</th>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Type</th>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Volume</th>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Order Price</th>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">S/L</th>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">T/P</th>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Current Price</th>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">State</th>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Placement Time</th>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredOrders.length === 0 ? (
              <tr>
                <td colSpan={12} className="text-center py-8 text-gray-400">
                  No records found.
                </td>
              </tr>
            ) : (
              filteredOrders.map((order) => (
                <tr 
                  key={order.orderId} 
                  className="border-b border-slate-700 hover:bg-slate-700/50 cursor-pointer select-none"
                  onContextMenu={(e) => handleContextMenu(e, order)}
                  onDoubleClick={() => handleDoubleClick(order)}
                  title="Right-click for options, double-click to modify"
                >
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{order.accountId}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{order.orderId}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-medium">{order.symbol}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm">
                    <span className="px-2 py-1 rounded text-xs bg-yellow-500/20 text-yellow-400">
                      {order.type}
                    </span>
                  </td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{order.volume}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPrice(order.orderPrice, order.symbol)}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPriceOrFallback(order.sl, order.symbol)}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPriceOrFallback(order.tp, order.symbol)}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPrice(order.currentPrice, order.symbol)}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm">
                    <span className="px-2 py-1 rounded text-xs bg-blue-500/20 text-blue-400">
                      {order.state}
                    </span>
                  </td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-gray-400">{order.placementTime}</td>
                  <td className="p-2 sm:p-3">
                    <div className="flex items-center space-x-1 sm:space-x-2">
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          setModifyingOrder(order);
                        }}
                        className="p-1 text-blue-400 hover:text-blue-300 transition-colors" 
                        title="Modify (or double-click row)"
                      >
                        <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                      </button>
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCancelOrder(order.orderId);
                        }}
                        className="p-1 text-red-400 hover:text-red-300 transition-colors" 
                        title="Cancel (or right-click for menu)"
                      >
                        <X className="w-3 h-3 sm:w-4 sm:h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Modals */}
      {showNewOrder && (
        <NewOrderModal
          isOpen={showNewOrder}
          onClose={() => setShowNewOrder(false)}
        />
      )}
      
      {modifyingOrder && (
        <ModifyOrderModal
          isOpen={!!modifyingOrder}
          order={modifyingOrder}
          onClose={() => setModifyingOrder(null)}
        />
      )}

      {/* Context Menu */}
      {contextMenu && (
        <div
          className="fixed bg-slate-800 border border-slate-600 rounded-lg shadow-xl z-50 py-1 min-w-[160px]"
          style={{
            left: `${contextMenu.x}px`,
            top: `${contextMenu.y}px`,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <button
            onClick={() => handleContextMenuAction('modify', contextMenu.order)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2"
          >
            <Edit className="w-4 h-4" />
            <span>Modify Order</span>
          </button>
          <button
            onClick={() => handleContextMenuAction('cancel', contextMenu.order)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2"
          >
            <X className="w-4 h-4" />
            <span>Cancel Order</span>
          </button>
          <div className="border-t border-slate-600 my-1"></div>
          <button
            onClick={() => handleContextMenuAction('copy', contextMenu.order)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2"
          >
            <Copy className="w-4 h-4" />
            <span>Copy Details</span>
          </button>
          <button
            onClick={() => handleContextMenuAction('details', contextMenu.order)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2"
          >
            <Eye className="w-4 h-4" />
            <span>View Details</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default PendingOrdersTab;
