// Trading-related types for the nexus-dealer-terminal application

export interface Position {
  positionId: string;
  accountId: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  volume: number;
  openPrice: number;
  currentPrice: number;
  sl?: number; // Stop Loss
  tp?: number; // Take Profit
  pl: number; // Profit/Loss
  openTime: string;
  comment?: string;
}

export interface Order {
  orderId: string;
  accountId: string;
  symbol: string;
  type: string;
  volume: number;
  orderPrice: number;
  sl?: number;
  tp?: number;
  placementTime: string;
  expiration: string;
  comment?: string;
}

export interface Quote {
  symbol: string;
  bid: number;
  ask: number;
  spread: number;
  time: string;
}

export interface TraderAccount {
  accountId: string;
  name: string;
  userGroup: string;
  balance: number;
  equity: number;
  margin: number;
  freeMargin: number;
  marginLevel: number;
  credit: number;
  profit: number;
}

export interface HistoryRecord {
  id: string;
  type: 'position' | 'order' | 'deal';
  accountId: string;
  symbol: string;
  volume: number;
  openPrice?: number;
  closePrice?: number;
  openTime: string;
  closeTime?: string;
  profit?: number;
  comment?: string;
}

// Context menu types
export interface ContextMenuState {
  x: number;
  y: number;
  position: Position;
}

export interface ContextMenuAction {
  label: string;
  action: () => void;
  destructive?: boolean;
  icon?: React.ReactNode;
}

// Filter types
export interface PositionFilters {
  userGroup: string;
  accountId: string;
  symbol: string;
}

export interface OrderFilters {
  userGroup: string;
  accountId: string;
  symbol: string;
  orderType: string;
}

// Error handling types
export interface ApiError {
  code: string;
  message: string;
  details?: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

// Modal props types
export interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export interface PositionModalProps extends BaseModalProps {
  position?: Position;
}

export interface OrderModalProps extends BaseModalProps {
  order?: Order;
}