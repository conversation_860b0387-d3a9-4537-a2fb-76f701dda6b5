<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">Platform Configuration - OTX Platform</title>
    <link rel="stylesheet" href="assets/styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo" id="header-logo">OTX</div>
            <div class="header-info">
                <h1>Platform Configuration</h1>
                <p>Set up your trading infrastructure</p>
            </div>
            <div class="progress-badge" id="progress-badge">Step 1 of 3</div>
        </div>
    </header>

    <div class="configuration-container">
        <!-- Progress Bar -->
        <div class="progress-section">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="steps-indicator">
                <div class="step active" id="step-indicator-1">
                    <div class="step-number">1</div>
                    <span>Company Info</span>
                </div>
                <div class="step" id="step-indicator-2">
                    <div class="step-number">2</div>
                    <span>Trading Config</span>
                </div>
                <div class="step" id="step-indicator-3">
                    <div class="step-number">3</div>
                    <span>Instruments</span>
                </div>
            </div>
        </div>

        <!-- Step 1: Company Information -->
        <div class="step-content active" id="step-1">
            <div class="step-header">
                <div class="step-icon">🏢</div>
                <h2>Company Information</h2>
                <p>Tell us about your brokerage company</p>
            </div>

            <form class="configuration-form" id="company-form">
                <div class="form-group">
                    <label for="companyName">Company Name *</label>
                    <input type="text" id="companyName" name="companyName" placeholder="Enter your company name" required>
                </div>

                <div class="form-group">
                    <label for="companyLogo">Company Logo</label>
                    <div class="file-upload">
                        <input type="file" id="companyLogo" name="companyLogo" accept=".png,.jpg,.jpeg,.svg">
                        <div class="upload-area" id="upload-area">
                            <div class="upload-icon">📁</div>
                            <p>Click to upload or drag and drop</p>
                            <small>PNG, JPG or SVG (MAX. 2MB)</small>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="contactEmail">Contact Email *</label>
                    <input type="email" id="contactEmail" name="contactEmail" placeholder="<EMAIL>" required>
                </div>

                <div class="form-group">
                    <label for="contactPhone">Contact Phone</label>
                    <input type="tel" id="contactPhone" name="contactPhone" placeholder="+****************">
                </div>

                <div class="form-group">
                    <label for="companyWebsite">Company Website</label>
                    <input type="url" id="companyWebsite" name="companyWebsite" placeholder="https://www.yourcompany.com">
                </div>
            </form>
        </div>

        <!-- Step 2: Trading Configuration -->
        <div class="step-content" id="step-2">
            <div class="step-header">
                <div class="step-icon">⚙️</div>
                <h2>Trading Configuration</h2>
                <p>Configure your trading platform settings</p>
            </div>

            <form class="configuration-form" id="trading-form">
                <div class="form-group">
                    <label for="userGroups">Number of User Groups</label>
                    <select id="userGroups" name="userGroups">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                    <small>Number of user groups to organize your clients</small>
                </div>

                <div class="form-group">
                    <label for="traderAccounts">Initial Trader Accounts</label>
                    <select id="traderAccounts" name="traderAccounts">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                    <small>Number of demo accounts to create initially</small>
                </div>

                <div class="configuration-summary" id="config-summary">
                    <h3>Configuration Summary</h3>
                    <ul>
                        <li>User groups: <span id="summary-user-groups">1</span></li>
                        <li>Initial demo accounts: <span id="summary-accounts">500</span></li>
                        <li>CRM system: Fully configured</li>
                        <li>Mobile app: White-label ready</li>
                        <li>Dealer terminal: Multi-user access</li>
                    </ul>
                </div>
            </form>
        </div>

        <!-- Step 3: Trading Instruments -->
        <div class="step-content" id="step-3">
            <div class="step-header">
                <div class="step-icon">📈</div>
                <h2>Trading Instruments</h2>
                <p id="instruments-description">Select the financial instruments your clients can trade (0 selected)</p>
            </div>

            <form class="configuration-form" id="instruments-form">
                <div class="instruments-grid" id="instruments-grid">
                    <!-- Instruments will be populated by JavaScript -->
                </div>

                <div class="validation-message hidden" id="validation-message">
                    Please select at least one symbol group to continue.
                </div>
            </form>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <button class="btn btn-secondary hidden" id="prev-btn">← Previous</button>
            <button class="btn btn-primary" id="next-btn">Next Step →</button>
            <button class="btn btn-primary hidden" id="submit-btn">
                <span class="btn-content">✅ Complete Setup</span>
                <span class="btn-loading hidden">
                    <span class="spinner"></span>
                    Configuring Platform...
                </span>
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/config.js"></script>
    <script src="assets/script.js"></script>
    <script>
        // Initialize the configuration page
        document.addEventListener('DOMContentLoaded', function() {
            initializeConfigurationPage();
        });
    </script>
</body>
</html>