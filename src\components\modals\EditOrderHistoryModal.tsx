import React, { useState, useEffect } from 'react';
import { X, AlertTriangle, Loader2 } from 'lucide-react';

interface EditOrderHistoryModalProps {
  isOpen: boolean;
  order: any;
  onClose: () => void;
}

const EditOrderHistoryModal = ({ isOpen, order, onClose }: EditOrderHistoryModalProps) => {
  const [formData, setFormData] = useState({
    volume: order?.volume || '',
    orderPrice: order?.orderPrice || '',
    placementTime: order?.placementTime || '',
    cancelTime: order?.cancelTime || ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // ESC key handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  const handleSubmit = async () => {
    setErrors({});
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      console.log('Order history updated:', { orderId: order.orderId, ...formData });
      onClose();
    } catch (error) {
      setErrors({ general: 'Failed to update order history' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen || !order) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700">
          <h3 className="text-lg font-semibold text-white">Edit Order History</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <div className="p-4 space-y-4">
          {/* Prominent Warning */}
          <div className="p-4 bg-orange-900/30 border border-orange-500/50 rounded-lg">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-6 h-6 text-orange-400 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="text-orange-400 font-semibold text-sm mb-1">WARNING</h4>
                <p className="text-orange-300 text-sm leading-relaxed">
                  You are editing a completed financial transaction. This action will be logged and audited. 
                  Only make changes if absolutely necessary and ensure all modifications are accurate.
                </p>
              </div>
            </div>
          </div>

          {errors.general && (
            <div className="p-3 bg-red-900/30 border border-red-800 rounded text-red-400 text-sm">
              {errors.general}
            </div>
          )}

          {/* Order Info */}
          <div className="bg-slate-700 rounded-lg p-3 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Order ID:</span>
              <span className="text-white">{order.orderId}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Account ID:</span>
              <span className="text-white">{order.accountId}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Symbol:</span>
              <span className="text-white font-medium">{order.symbol}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Type:</span>
              <span className="text-white">{order.type}</span>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Volume (lots)</label>
              <input
                type="number"
                step="0.01"
                value={formData.volume}
                onChange={(e) => setFormData({ ...formData, volume: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Order Price</label>
              <input
                type="number"
                step="0.00001"
                value={formData.orderPrice}
                onChange={(e) => setFormData({ ...formData, orderPrice: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Placement Time</label>
              <input
                type="text"
                value={formData.placementTime}
                onChange={(e) => setFormData({ ...formData, placementTime: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="YYYY-MM-DD HH:MM:SS"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Cancel Time</label>
              <input
                type="text"
                value={formData.cancelTime}
                onChange={(e) => setFormData({ ...formData, cancelTime: e.target.value })}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="YYYY-MM-DD HH:MM:SS"
              />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-4 border-t border-slate-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading}
            className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Updating...</span>
              </>
            ) : (
              <span>Update Order</span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditOrderHistoryModal; 