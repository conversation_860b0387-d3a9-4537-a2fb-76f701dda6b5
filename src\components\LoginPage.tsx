import React, { useState, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { AlertCircle, Loader2, Shield } from 'lucide-react';
import ReCAPTCHA from 'react-google-recaptcha';

const LoginPage = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [captchaValue, setCaptchaValue] = useState<string | null>(null);
  const { login, isLoading } = useAuth();
  const recaptchaRef = useRef<ReCAPTCHA>(null);

  // Google reCAPTCHA test site key for development
  // Replace with your production key when deploying
  const RECAPTCHA_SITE_KEY = import.meta.env.VITE_RECAPTCHA_SITE_KEY || "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI";

  const handleCaptchaChange = (value: string | null) => {
    setCaptchaValue(value);
    if (error === 'Please complete the CAPTCHA verification') {
      setError('');
    }
  };

  const handleLogin = async () => {
    setError('');

    // Client-side validation
    if (!username.trim()) {
      setError('Username is required.');
      return;
    }

    if (!password.trim()) {
      setError('Password is required.');
      return;
    }

    // Check for demo credentials from broker onboarding
    const brokerData = localStorage.getItem('brokerOnboarding');
    let isDemoLogin = false;

    if (brokerData) {
      const data = JSON.parse(brokerData);
      const companyName = data.configuration?.companyName?.toLowerCase().replace(/\s+/g, '') || '';
      const demoUsername = `dealer@${companyName}.com`;
      const demoPassword = 'Dealer123!';

      if (username === demoUsername && password === demoPassword) {
        isDemoLogin = true;
      }
    }

    // Skip CAPTCHA for demo login
    if (!isDemoLogin && !captchaValue) {
      setError('Please complete the CAPTCHA verification');
      return;
    }

    const success = await login(username, password, captchaValue || 'demo-bypass');
    if (!success) {
      setError('Invalid credentials or CAPTCHA verification failed');
      // Reset reCAPTCHA on failed login
      if (!isDemoLogin) {
        recaptchaRef.current?.reset();
        setCaptchaValue(null);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isLoading && (captchaValue || isDemoCredentialsAvailable())) {
      handleLogin();
    }
  };

  const isDemoCredentialsAvailable = () => {
    const brokerData = localStorage.getItem('brokerOnboarding');
    return brokerData && JSON.parse(brokerData).status === 'configured';
  };

  const getDemoCredentials = () => {
    const brokerData = localStorage.getItem('brokerOnboarding');
    if (!brokerData) return null;

    const data = JSON.parse(brokerData);
    const companyName = data.configuration?.companyName?.toLowerCase().replace(/\s+/g, '') || '';
    return {
      username: `dealer@${companyName}.com`,
      password: 'Dealer123!',
      companyName: data.configuration?.companyName || ''
    };
  };

  const fillDemoCredentials = () => {
    const demo = getDemoCredentials();
    if (demo) {
      setUsername(demo.username);
      setPassword(demo.password);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo and Title */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <span className="text-white font-bold text-2xl">OTX</span>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">OTX Dealer Platform</h1>
          <p className="text-gray-400">Professional Trading Terminal</p>
        </div>

        {/* Login Form */}
        <div className="bg-slate-800 rounded-xl p-8 shadow-2xl border border-slate-700">
          <h2 className="text-xl font-semibold text-white mb-6 text-center">Dealer Login</h2>
          
          <div className="space-y-4">
            <div>
              <input
                type="text"
                placeholder="Enter your username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                onKeyPress={handleKeyPress}
                className="w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <input
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyPress={handleKeyPress}
                className="w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                disabled={isLoading}
              />
            </div>

            {/* Demo Credentials Helper */}
            {isDemoCredentialsAvailable() && (
              <div className="p-3 bg-blue-900/20 border border-blue-700 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-blue-400">Demo Access Available</h4>
                    <p className="text-xs text-blue-300">
                      Use your broker demo credentials for {getDemoCredentials()?.companyName}
                    </p>
                  </div>
                  <Button
                    type="button"
                    size="sm"
                    onClick={fillDemoCredentials}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium text-xs"
                  >
                    Fill Demo
                  </Button>
                </div>
              </div>
            )}

            {/* Google reCAPTCHA - Skip for demo users */}
            {!isDemoCredentialsAvailable() && (
              <div className="flex justify-center">
                <ReCAPTCHA
                  ref={recaptchaRef}
                  sitekey={RECAPTCHA_SITE_KEY}
                  onChange={handleCaptchaChange}
                  theme="dark"
                  size="normal"
                />
              </div>
            )}
            
            {error && (
              <div className="flex items-center space-x-2 text-red-400 text-sm bg-red-900/30 border border-red-800 rounded-lg p-3">
                <AlertCircle className="w-4 h-4" />
                <span>{error}</span>
              </div>
            )}
            
            <button
              onClick={handleLogin}
              disabled={isLoading || (!captchaValue && !isDemoCredentialsAvailable())}
              className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold py-3 px-4 rounded-lg hover:from-blue-600 hover:to-cyan-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Authenticating...</span>
                </>
              ) : (
                <>
                  <Shield className="w-4 h-4" />
                  <span>Login</span>
                </>
              )}
            </button>
          </div>
          
          <div className="mt-6 text-center text-sm text-gray-400">
            <p>Demo credentials: dealer01 / password123</p>
            <p className="mt-1 text-xs">Complete CAPTCHA verification to login</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
