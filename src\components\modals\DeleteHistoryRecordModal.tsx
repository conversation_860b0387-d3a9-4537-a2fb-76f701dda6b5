import React, { useState, useEffect } from 'react';
import { X, AlertTriangle, Trash2, Loader2 } from 'lucide-react';

interface DeleteHistoryRecordModalProps {
  isOpen: boolean;
  record: any;
  recordType: 'position' | 'order' | 'deal';
  onClose: () => void;
  onConfirm: (recordId: string) => Promise<void>;
}

const DeleteHistoryRecordModal = ({ 
  isOpen, 
  record, 
  recordType, 
  onClose, 
  onConfirm 
}: DeleteHistoryRecordModalProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmationInput, setConfirmationInput] = useState('');
  const [error, setError] = useState('');

  // ESC key handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  if (!isOpen || !record) return null;

  const getRecordTitle = () => {
    switch (recordType) {
      case 'position':
        return 'Position History Record';
      case 'order':
        return 'Order History Record';
      case 'deal':
        return 'Deal History Record';
      default:
        return 'History Record';
    }
  };

  const getRecordId = () => {
    switch (recordType) {
      case 'position':
        return record.positionId;
      case 'order':
        return record.orderId;
      case 'deal':
        return record.dealId;
      default:
        return record.id;
    }
  };

  const requiredConfirmation = 'DELETE';

  const handleDelete = async () => {
    setError('');
    
    if (confirmationInput !== requiredConfirmation) {
      setError(`Please type "${requiredConfirmation}" to confirm deletion.`);
      return;
    }

    setIsDeleting(true);
    try {
      await onConfirm(getRecordId());
      onClose();
    } catch (err) {
      setError('Failed to delete record. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700">
          <div className="flex items-center space-x-3">
            <Trash2 className="w-5 h-5 text-red-400" />
            <h3 className="text-lg font-semibold text-white">Delete {getRecordTitle()}</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <div className="p-4 space-y-4">
          {/* Critical Warning */}
          <div className="p-4 bg-red-900/30 border border-red-500/50 rounded-lg">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-6 h-6 text-red-400 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="text-red-400 font-semibold text-sm mb-1">CRITICAL WARNING</h4>
                <p className="text-red-300 text-sm leading-relaxed">
                  You are about to permanently delete a completed financial transaction record. 
                  This action cannot be undone and will be logged for audit purposes. 
                  Only proceed if you have explicit authorization to delete historical records.
                </p>
              </div>
            </div>
          </div>

          {error && (
            <div className="p-3 bg-red-900/30 border border-red-800 rounded text-red-400 text-sm">
              {error}
            </div>
          )}

          {/* Record Information */}
          <div className="bg-slate-700/50 rounded-lg p-3 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Record Type:</span>
              <span className="text-white">{getRecordTitle()}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">
                {recordType === 'position' ? 'Position ID:' : 
                 recordType === 'order' ? 'Order ID:' : 'Deal ID:'}
              </span>
              <span className="text-white">{getRecordId()}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Account ID:</span>
              <span className="text-white">{record.accountId}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Symbol:</span>
              <span className="text-white font-medium">{record.symbol}</span>
            </div>
            {recordType === 'position' && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Final P/L:</span>
                <span className={`font-medium ${record.finalPL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {record.finalPL >= 0 ? '+' : ''}${record.finalPL?.toFixed(2)}
                </span>
              </div>
            )}
          </div>

          {/* Confirmation Input */}
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">
              Type <span className="text-red-400 font-bold">"{requiredConfirmation}"</span> to confirm deletion:
            </label>
            <input
              type="text"
              value={confirmationInput}
              onChange={(e) => setConfirmationInput(e.target.value)}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder={`Type ${requiredConfirmation} here`}
              autoComplete="off"
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-4 border-t border-slate-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleDelete}
            disabled={isDeleting || confirmationInput !== requiredConfirmation}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isDeleting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Deleting...</span>
              </>
            ) : (
              <>
                <Trash2 className="w-4 h-4" />
                <span>Delete Record</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteHistoryRecordModal; 