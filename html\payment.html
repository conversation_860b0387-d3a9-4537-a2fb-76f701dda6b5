<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">Complete Your Purchase - OTX Platform</title>
    <link rel="stylesheet" href="assets/styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <button class="btn btn-ghost" id="back-btn">
                    ← Back
                </button>
                <div class="logo" id="header-logo">OTX</div>
                <div class="header-info">
                    <h1>Complete Your Purchase</h1>
                    <p id="header-subtitle">Secure checkout powered by OTX Platform</p>
                </div>
            </div>
            <div class="ssl-badge">
                🛡️ <span id="ssl-text">SSL Secured</span>
            </div>
        </div>
    </header>

    <div class="payment-container">
        <!-- Order Summary -->
        <aside class="order-summary">
            <div class="summary-card">
                <h2 class="card-title">
                    🏢 Order Summary
                </h2>
                
                <div class="plan-details" id="plan-details">
                    <div class="plan-header">
                        <div class="plan-info">
                            <h3 id="selected-plan-name">Professional Plan</h3>
                            <p id="selected-plan-description">Ideal for growing brokerages with advanced needs</p>
                        </div>
                        <div class="popular-badge" id="popular-badge">Popular</div>
                    </div>
                    <div class="plan-price">
                        <span id="selected-plan-price">$5,999</span>
                        <span class="period" id="selected-plan-period">/month</span>
                    </div>
                </div>

                <div class="features-section">
                    <h4>Included Features:</h4>
                    <ul class="features-list" id="plan-features">
                        <!-- Features will be populated by JavaScript -->
                    </ul>
                </div>

                <div class="pricing-breakdown">
                    <div class="price-row">
                        <span>Subtotal</span>
                        <span id="subtotal">$5,999</span>
                    </div>
                    <div class="price-row">
                        <span>Setup Fee</span>
                        <span class="free" id="setup-fee">Free</span>
                    </div>
                    <div class="price-row">
                        <span>First Month</span>
                        <span class="discount" id="first-month-discount">50% Off</span>
                    </div>
                    <hr class="divider">
                    <div class="price-row total">
                        <span>Total Today</span>
                        <span id="total-today">$2,999</span>
                    </div>
                </div>

                <div class="guarantees" id="guarantees">
                    <!-- Guarantees will be populated by JavaScript -->
                </div>
            </div>
        </aside>

        <!-- Payment Form -->
        <main class="payment-form">
            <form id="payment-form">
                <!-- Billing Information -->
                <section class="form-section">
                    <h2 class="section-title">
                        👤 Billing Information
                    </h2>
                    <p class="section-description">Enter your billing details for the subscription</p>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">First Name *</label>
                            <input type="text" id="firstName" name="firstName" required>
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name *</label>
                            <input type="text" id="lastName" name="lastName" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="company">Company Name *</label>
                        <input type="text" id="company" name="company" required>
                    </div>
                </section>

                <!-- Payment Information -->
                <section class="form-section">
                    <h2 class="section-title">
                        💳 Payment Information
                    </h2>
                    <p class="section-description">Your payment information is encrypted and secure</p>
                    
                    <div class="form-group">
                        <label for="cardholderName">Cardholder Name *</label>
                        <input type="text" id="cardholderName" name="cardholderName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="cardNumber">Card Number *</label>
                        <input type="text" id="cardNumber" name="cardNumber" placeholder="1234 5678 9012 3456" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="expiryDate">Expiry Date *</label>
                            <input type="text" id="expiryDate" name="expiryDate" placeholder="MM/YY" required>
                        </div>
                        <div class="form-group">
                            <label for="cvv">CVV *</label>
                            <input type="text" id="cvv" name="cvv" placeholder="123" required>
                        </div>
                    </div>
                </section>

                <!-- Billing Address -->
                <section class="form-section">
                    <h2 class="section-title">Billing Address</h2>
                    
                    <div class="form-group">
                        <label for="address">Street Address *</label>
                        <input type="text" id="address" name="address" required>
                    </div>
                    
                    <div class="form-row-3">
                        <div class="form-group">
                            <label for="city">City *</label>
                            <input type="text" id="city" name="city" required>
                        </div>
                        <div class="form-group">
                            <label for="state">State *</label>
                            <input type="text" id="state" name="state" required>
                        </div>
                        <div class="form-group">
                            <label for="zipCode">ZIP Code *</label>
                            <input type="text" id="zipCode" name="zipCode" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="country">Country *</label>
                        <select id="country" name="country" required>
                            <option value="United States">United States</option>
                            <option value="Canada">Canada</option>
                            <option value="United Kingdom">United Kingdom</option>
                            <option value="Australia">Australia</option>
                            <option value="Germany">Germany</option>
                            <option value="France">France</option>
                        </select>
                    </div>
                </section>

                <!-- Submit Button -->
                <div class="submit-section">
                    <button type="submit" class="btn btn-primary btn-lg submit-btn" id="submit-btn">
                        <span class="btn-content">
                            🔒 <span>Complete Purchase - </span><span id="submit-total">$2,999</span>
                        </span>
                        <span class="btn-loading hidden">
                            <span class="spinner"></span>
                            Processing Payment...
                        </span>
                    </button>
                </div>

                <div class="terms" id="terms-section">
                    <!-- Terms will be populated by JavaScript -->
                </div>
            </form>
        </main>
    </div>

    <!-- Scripts -->
    <script src="assets/config.js"></script>
    <script src="assets/script.js"></script>
    <script>
        // Initialize the payment page
        document.addEventListener('DOMContentLoaded', function() {
            initializePaymentPage();
        });
    </script>
</body>
</html>