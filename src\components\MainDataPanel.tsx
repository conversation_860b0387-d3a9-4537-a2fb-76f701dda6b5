import React, { useState } from 'react';
import { useWebSocket } from '../contexts/WebSocketContext';
import OpenPositionsTab from './tabs/OpenPositionsTab';
import PendingOrdersTab from './tabs/PendingOrdersTab';
import HistoryTabs from './tabs/HistoryTabs';
import AuditLogTab from './tabs/AuditLogTab';

const MainDataPanel = () => {
  const [activeTab, setActiveTab] = useState('positions');

  const tabs = [
    { id: 'positions', label: 'Open Positions' },
    { id: 'orders', label: 'Pending Orders' },
    { id: 'position-history', label: 'Position History' },
    { id: 'order-history', label: 'Order History' },
    { id: 'deal-history', label: 'Deal History' },
    { id: 'audit-log', label: 'Audit Log' },
  ];

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'positions':
        return <OpenPositionsTab />;
      case 'orders':
        return <PendingOrdersTab />;
      case 'position-history':
      case 'order-history':
      case 'deal-history':
        return <HistoryTabs activeTab={activeTab} />;
      case 'audit-log':
        return <AuditLogTab />;
      default:
        return <OpenPositionsTab />;
    }
  };

  return (
    <div className="bg-slate-800 rounded-lg border border-slate-700 w-full h-full flex flex-col">
      {/* Tab Navigation */}
      <div className="border-b border-slate-700 flex-shrink-0">
        <nav className="flex space-x-1 p-1 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors whitespace-nowrap ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-slate-700'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content - Fill remaining height */}
      <div className="flex-1 min-h-0 p-2 sm:p-4">
        {renderActiveTab()}
      </div>
    </div>
  );
};

export default MainDataPanel;
