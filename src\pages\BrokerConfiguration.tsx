import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  Upload, 
  Users, 
  TrendingUp, 
  CheckCircle, 
  ArrowRight,
  ArrowLeft,
  Settings,
  Globe,
  Coins
} from 'lucide-react';

const BrokerConfiguration = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [formData, setFormData] = useState({
    // Company Information
    companyName: '',
    companyLogo: null as File | null,
    contactEmail: '',
    contactPhone: '',
    website: '',
    
    // Trading Configuration
    userGroups: '3',
    traderAccounts: '700',
    
    // Symbol Groups
    symbolGroups: {
      forexMajors: false,
      forexMinors: false,
      bullion: false
    }
  });

  useEffect(() => {
    // Check if user came from payment flow
    const brokerData = localStorage.getItem('brokerOnboarding');
    if (!brokerData) {
      navigate('/broker/payment');
      return;
    }
    
    const data = JSON.parse(brokerData);
    setFormData(prev => ({
      ...prev,
      companyName: data.billingInfo.company || '',
      contactEmail: data.billingInfo.email || ''
    }));
  }, [navigate]);

  const symbolGroupOptions = [
    {
      id: 'forexMajors',
      name: 'Forex Majors',
      description: 'EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD, USD/CAD, NZD/USD',
      icon: <Globe className="w-5 h-5" />,
      popular: true
    },
    {
      id: 'forexMinors',
      name: 'Forex Minors',
      description: 'EUR/GBP, EUR/JPY, GBP/JPY, AUD/CAD, EUR/AUD, GBP/CAD',
      icon: <Globe className="w-5 h-5" />
    },
    {
      id: 'bullion',
      name: 'Precious Metals',
      description: 'Gold (XAU/USD), Silver (XAG/USD), Platinum, Palladium',
      icon: <Coins className="w-5 h-5" />,
      popular: true
    }
  ];

  const steps = [
    { number: 1, title: 'Company Information', icon: <Building2 className="w-5 h-5" /> },
    { number: 2, title: 'Trading Configuration', icon: <Settings className="w-5 h-5" /> },
    { number: 3, title: 'Symbol Groups', icon: <TrendingUp className="w-5 h-5" /> }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSymbolGroupChange = (groupId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      symbolGroups: {
        ...prev.symbolGroups,
        [groupId]: checked
      }
    }));
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        companyLogo: file
      }));
    }
  };

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    // Simulate configuration processing
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Store complete configuration
    const brokerData = JSON.parse(localStorage.getItem('brokerOnboarding') || '{}');
    const completeConfig = {
      ...brokerData,
      configuration: formData,
      configurationDate: new Date().toISOString(),
      status: 'configured'
    };
    
    localStorage.setItem('brokerOnboarding', JSON.stringify(completeConfig));
    
    setIsSubmitting(false);
    navigate('/broker/dashboard');
  };

  const progress = (currentStep / 3) * 100;
  const selectedSymbolGroups = Object.values(formData.symbolGroups).filter(Boolean).length;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Header */}
      <header className="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">OTX</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">Platform Configuration</h1>
                <p className="text-sm text-gray-400">Set up your trading infrastructure</p>
              </div>
            </div>
            <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
              Step {currentStep} of 3
            </Badge>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            {steps.map((step) => (
              <div 
                key={step.number}
                className={`flex items-center space-x-2 ${
                  step.number <= currentStep ? 'text-blue-400' : 'text-gray-500'
                }`}
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                  step.number <= currentStep 
                    ? 'border-blue-400 bg-blue-400/20' 
                    : 'border-gray-500'
                }`}>
                  {step.number < currentStep ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : (
                    step.icon
                  )}
                </div>
                <span className="font-medium hidden sm:block">{step.title}</span>
              </div>
            ))}
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Step 1: Company Information */}
        {currentStep === 1 && (
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Building2 className="w-6 h-6 mr-3" />
                Company Information
              </CardTitle>
              <CardDescription className="text-gray-400">
                Tell us about your brokerage company
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="companyName" className="text-white">Company Name *</Label>
                <Input
                  id="companyName"
                  value={formData.companyName}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                  className="bg-slate-700 border-slate-600 text-white"
                  placeholder="Enter your company name"
                  required
                />
              </div>

              <div>
                <Label htmlFor="companyLogo" className="text-white">Company Logo</Label>
                <div className="mt-2">
                  <div className="flex items-center justify-center w-full">
                    <label htmlFor="companyLogo" className="flex flex-col items-center justify-center w-full h-32 border-2 border-slate-600 border-dashed rounded-lg cursor-pointer bg-slate-700/50 hover:bg-slate-700">
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="w-8 h-8 mb-4 text-gray-400" />
                        <p className="mb-2 text-sm text-gray-400">
                          <span className="font-semibold">Click to upload</span> or drag and drop
                        </p>
                        <p className="text-xs text-gray-400">PNG, JPG or SVG (MAX. 2MB)</p>
                        {formData.companyLogo && (
                          <p className="text-xs text-blue-400 mt-2">
                            Selected: {formData.companyLogo.name}
                          </p>
                        )}
                      </div>
                      <input
                        id="companyLogo"
                        type="file"
                        className="hidden"
                        accept="image/*"
                        onChange={handleFileUpload}
                      />
                    </label>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contactEmail" className="text-white">Contact Email *</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={formData.contactEmail}
                    onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="contactPhone" className="text-white">Contact Phone</Label>
                  <Input
                    id="contactPhone"
                    value={formData.contactPhone}
                    onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="+****************"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="website" className="text-white">Company Website</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  className="bg-slate-700 border-slate-600 text-white"
                  placeholder="https://www.yourcompany.com"
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Trading Configuration */}
        {currentStep === 2 && (
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Settings className="w-6 h-6 mr-3" />
                Trading Configuration
              </CardTitle>
              <CardDescription className="text-gray-400">
                Configure your trading platform settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="userGroups" className="text-white">Number of User Groups</Label>
                  <Select value={formData.userGroups} onValueChange={(value) => handleInputChange('userGroups', value)}>
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 user group</SelectItem>
                      <SelectItem value="2">2 user groups</SelectItem>
                      <SelectItem value="3">3 user groups</SelectItem>
                      <SelectItem value="4">4 user groups</SelectItem>
                      <SelectItem value="5">5 user groups</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-400 mt-1">
                    Number of user groups to organize your clients
                  </p>
                </div>

                <div>
                  <Label htmlFor="traderAccounts" className="text-white">Initial Trader Accounts</Label>
                  <Select value={formData.traderAccounts} onValueChange={(value) => handleInputChange('traderAccounts', value)}>
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="500">500 accounts</SelectItem>
                      <SelectItem value="600">600 accounts</SelectItem>
                      <SelectItem value="700">700 accounts</SelectItem>
                      <SelectItem value="800">800 accounts</SelectItem>
                      <SelectItem value="900">900 accounts</SelectItem>
                      <SelectItem value="1000">1000 accounts</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-400 mt-1">
                    Number of demo accounts to create initially
                  </p>
                </div>
              </div>

              <div className="p-4 bg-slate-700/30 rounded-lg">
                <h3 className="text-white font-medium mb-2 flex items-center">
                  <Users className="w-4 h-4 mr-2" />
                  Configuration Summary
                </h3>
                <div className="space-y-1 text-sm text-gray-300">
                  <p>• User groups: {formData.userGroups}</p>
                  <p>• Initial demo accounts: {formData.traderAccounts}</p>
                  <p>• CRM system: Fully configured</p>
                  <p>• Mobile app: White-label ready</p>
                  <p>• Dealer terminal: Multi-user access</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Symbol Groups */}
        {currentStep === 3 && (
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <TrendingUp className="w-6 h-6 mr-3" />
                Trading Instruments
              </CardTitle>
              <CardDescription className="text-gray-400">
                Select the financial instruments your clients can trade ({selectedSymbolGroups} selected)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {symbolGroupOptions.map((group) => (
                  <div 
                    key={group.id}
                    className={`relative p-4 border rounded-lg cursor-pointer transition-all ${
                      formData.symbolGroups[group.id as keyof typeof formData.symbolGroups]
                        ? 'border-blue-500 bg-blue-500/10'
                        : 'border-slate-600 hover:border-slate-500'
                    }`}
                    onClick={() => handleSymbolGroupChange(
                      group.id, 
                      !formData.symbolGroups[group.id as keyof typeof formData.symbolGroups]
                    )}
                  >
                    {group.popular && (
                      <Badge className="absolute -top-2 -right-2 bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                        Popular
                      </Badge>
                    )}
                    <div className="flex items-start space-x-3">
                      <Checkbox
                        checked={formData.symbolGroups[group.id as keyof typeof formData.symbolGroups]}
                        onChange={() => {}}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="text-blue-400">{group.icon}</div>
                          <h3 className="font-medium text-white">{group.name}</h3>
                        </div>
                        <p className="text-sm text-gray-400">{group.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {selectedSymbolGroups === 0 && (
                <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-700 rounded-lg">
                  <p className="text-yellow-400 text-sm">
                    Please select at least one symbol group to continue.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1}
            className="border-slate-600 text-white hover:bg-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:border-slate-700 disabled:text-slate-500"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Previous
          </Button>

          {currentStep < 3 ? (
            <Button
              onClick={nextStep}
              disabled={
                (currentStep === 1 && (!formData.companyName || !formData.contactEmail)) ||
                (currentStep === 3 && selectedSymbolGroups === 0)
              }
              className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next Step
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={selectedSymbolGroups === 0 || isSubmitting}
              className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Configuring Platform...
                </>
              ) : (
                <>
                  Complete Setup
                  <CheckCircle className="w-4 h-4 ml-2" />
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default BrokerConfiguration;
