import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  Users, 
  Shield, 
  Zap, 
  BarChart3, 
  Globe, 
  CheckCircle,
  ArrowRight,
  Star,
  Building2,
  Smartphone,
  Monitor
} from 'lucide-react';

const BrokerLanding = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: <Users className="w-8 h-8 text-blue-400" />,
      title: "Complete CRM System",
      description: "Manage clients, leads, and relationships with our comprehensive CRM platform designed for trading businesses."
    },
    {
      icon: <Smartphone className="w-8 h-8 text-green-400" />,
      title: "Mobile Trading App",
      description: "White-label mobile application for your clients with real-time trading, charts, and portfolio management."
    },
    {
      icon: <Monitor className="w-8 h-8 text-purple-400" />,
      title: "Professional Dealer Terminal",
      description: "Advanced dealer terminal for risk management, position monitoring, and real-time market operations."
    },
    {
      icon: <Shield className="w-8 h-8 text-red-400" />,
      title: "Enterprise Security",
      description: "Bank-grade security with multi-factor authentication, encryption, and compliance features."
    },
    {
      icon: <Zap className="w-8 h-8 text-yellow-400" />,
      title: "Lightning Fast Setup",
      description: "Get your trading business operational in minutes, not months. Complete platform deployment in under 24 hours."
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-cyan-400" />,
      title: "Advanced Analytics",
      description: "Comprehensive reporting, risk analytics, and business intelligence tools to grow your brokerage."
    }
  ];

  const plans = [
    {
      name: "Starter",
      price: "$2,999",
      period: "/month",
      description: "Perfect for new brokers starting their trading business",
      features: [
        "Up to 100 client accounts",
        "Basic CRM functionality",
        "Mobile trading app",
        "Dealer terminal access",
        "Email support",
        "Standard reporting"
      ],
      popular: false
    },
    {
      name: "Professional",
      price: "$5,999",
      period: "/month",
      description: "Ideal for growing brokerages with advanced needs",
      features: [
        "Up to 500 client accounts",
        "Advanced CRM with automation",
        "White-label mobile app",
        "Multi-dealer terminal",
        "24/7 phone support",
        "Advanced analytics",
        "Custom integrations",
        "Risk management tools"
      ],
      popular: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "",
      description: "Tailored solutions for large-scale operations",
      features: [
        "Unlimited client accounts",
        "Full platform customization",
        "Dedicated infrastructure",
        "Multiple trading venues",
        "Dedicated account manager",
        "Custom development",
        "SLA guarantees",
        "Regulatory compliance"
      ],
      popular: false
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Header */}
      <header className="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">OTX</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">OTX Platform</h1>
                <p className="text-sm text-gray-400">Complete Trading Infrastructure</p>
              </div>
            </div>
            <Button 
              onClick={() => navigate('/broker/payment')}
              className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
            >
              Get Started
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <Badge className="mb-6 bg-blue-500/20 text-blue-400 border-blue-500/30">
            🚀 Launch Your Trading Business Today
          </Badge>
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            Complete Trading
            <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              {" "}Platform Solution
            </span>
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Everything you need to start and scale your brokerage business. CRM, mobile trading app, 
            and professional dealer terminal - all integrated and ready to deploy.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg"
              onClick={() => navigate('/broker/payment')}
              className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-lg px-8 py-4"
            >
              Start Your Brokerage
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            <Button 
              size="lg"
              variant="outline"
              onClick={() => navigate('/demo')}
              className="border-slate-400 bg-slate-800/30 text-white hover:bg-slate-700 hover:border-slate-300 transition-all duration-200 text-lg px-8 py-4"
            >
              View Demo
              <Globe className="w-5 h-5 ml-2" />
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-slate-800/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Everything You Need in One Platform
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Our integrated solution provides all the tools and infrastructure needed to run a successful trading business.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                <CardHeader>
                  <div className="mb-4">{feature.icon}</div>
                  <CardTitle className="text-white">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-300">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Choose Your Plan
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Flexible pricing options to match your business size and growth plans.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <Card 
                key={index} 
                className={`relative ${
                  plan.popular 
                    ? 'bg-gradient-to-b from-blue-900/50 to-slate-800/50 border-blue-500' 
                    : 'bg-slate-800/50 border-slate-700'
                } hover:scale-105 transition-transform`}
              >
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white">
                    <Star className="w-3 h-3 mr-1" />
                    Most Popular
                  </Badge>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-white text-2xl">{plan.name}</CardTitle>
                  <div className="text-4xl font-bold text-white">
                    {plan.price}
                    <span className="text-lg text-gray-400">{plan.period}</span>
                  </div>
                  <CardDescription className="text-gray-300">
                    {plan.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-300">
                        <CheckCircle className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button 
                    className={`w-full ${
                      plan.popular 
                        ? 'bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600' 
                        : 'bg-slate-700 hover:bg-slate-600'
                    }`}
                    onClick={() => navigate('/broker/payment', { state: { selectedPlan: plan } })}
                  >
                    {plan.name === 'Enterprise' ? 'Contact Sales' : 'Get Started'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-900/50 to-cyan-900/50">
        <div className="max-w-4xl mx-auto text-center">
          <Building2 className="w-16 h-16 text-blue-400 mx-auto mb-6" />
          <h2 className="text-4xl font-bold text-white mb-4">
            Ready to Launch Your Trading Business?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join hundreds of successful brokers who trust OTX Platform for their trading infrastructure.
          </p>
          <Button 
            size="lg"
            onClick={() => navigate('/broker/payment')}
            className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-lg px-12 py-4"
          >
            Start Your Free Trial
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-slate-700 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center space-x-4 mb-4">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">OTX</span>
            </div>
            <span className="text-white font-semibold">OTX Platform</span>
          </div>
          <p className="text-gray-400">
            © 2024 OTX Platform. All rights reserved. Complete trading infrastructure for modern brokerages.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default BrokerLanding;
