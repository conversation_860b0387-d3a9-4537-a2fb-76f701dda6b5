import React, { useState, useEffect } from 'react';
import { X, Loader2 } from 'lucide-react';
import { useWebSocket } from '../../contexts/WebSocketContext';

interface ClosePositionModalProps {
  isOpen: boolean;
  position: any;
  onClose: () => void;
}

const ClosePositionModal = ({ isOpen, position, onClose }: ClosePositionModalProps) => {
  const { closePosition } = useWebSocket();
  const [volumeToClose, setVolumeToClose] = useState(position?.volume || '');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // ESC key handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  const handleSubmit = async () => {
    setErrors({});
    
    // Validation
    const volume = Number(volumeToClose);
    if (isNaN(volume) || volume <= 0) {
      setErrors({ volume: 'Please enter a valid volume.' });
      return;
    }
    
    if (volume > Number(position.volume)) {
      setErrors({ volume: 'Close volume cannot exceed position volume.' });
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Close position using context
      closePosition(position.positionId, volume);
      console.log('Position closed:', { positionId: position.positionId, volume: volumeToClose });
      onClose();
    } catch (error) {
      setErrors({ general: 'Position not found' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen || !position) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700">
          <h3 className="text-lg font-semibold text-white">Close Position</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <div className="p-4 space-y-4">
          {errors.general && (
            <div className="p-3 bg-red-900/30 border border-red-800 rounded text-red-400 text-sm">
              {errors.general}
            </div>
          )}

          {/* Position Info */}
          <div className="bg-slate-700 rounded-lg p-4 space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Position ID:</span>
              <span className="text-white">{position.positionId}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Symbol:</span>
              <span className="text-white font-medium">{position.symbol}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Type:</span>
              <span className={`px-2 py-1 rounded text-xs ${
                position.type === 'BUY' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
              }`}>
                {position.type}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Current Volume:</span>
              <span className="text-white font-mono">{position.volume}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Current P/L:</span>
              <span className={`font-mono ${position.pl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {position.pl >= 0 ? '+' : ''}${position.pl.toFixed(2)}
              </span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-1">Volume to Close</label>
            <input
              type="number"
              step="0.01"
              min="0.01"
              max={position.volume}
              value={volumeToClose}
              onChange={(e) => setVolumeToClose(e.target.value)}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.volume && <p className="text-red-400 text-xs mt-1">{errors.volume}</p>}
            <p className="text-xs text-gray-400 mt-1">
              Leave as {position.volume} for full close, or enter a smaller amount for partial close.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-4 border-t border-slate-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Closing...</span>
              </>
            ) : (
              <span>Confirm Close</span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ClosePositionModal;
