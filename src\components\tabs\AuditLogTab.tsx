import React, { useState, useMemo } from 'react';
import { Search, Calendar, Filter, Eye, AlertTriangle, User, Activity, Trash2, Edit, Plus, X, LogIn, LogOut } from 'lucide-react';
import { useWebSocket } from '../../contexts/WebSocketContext';

const AuditLogTab = () => {
  const { auditLog } = useWebSocket();
  const [filters, setFilters] = useState({
    userId: 'all',
    action: 'all',
    entityType: 'all',
    entityId: '',
    accountId: '',
    symbol: '',
    dateFrom: '',
    dateTo: ''
  });
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'CREATE':
        return <Plus className="w-4 h-4 text-green-400" />;
      case 'EDIT':
        return <Edit className="w-4 h-4 text-blue-400" />;
      case 'DELETE':
        return <Trash2 className="w-4 h-4 text-red-400" />;
      case 'CLOSE':
        return <X className="w-4 h-4 text-orange-400" />;
      case 'CANCEL':
        return <X className="w-4 h-4 text-yellow-400" />;
      case 'LOGIN':
        return <LogIn className="w-4 h-4 text-cyan-400" />;
      case 'LOGOUT':
        return <LogOut className="w-4 h-4 text-gray-400" />;
      default:
        return <Activity className="w-4 h-4 text-gray-400" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'CREATE':
        return 'text-green-400 bg-green-500/20';
      case 'EDIT':
        return 'text-blue-400 bg-blue-500/20';
      case 'DELETE':
        return 'text-red-400 bg-red-500/20';
      case 'CLOSE':
        return 'text-orange-400 bg-orange-500/20';
      case 'CANCEL':
        return 'text-yellow-400 bg-yellow-500/20';
      case 'LOGIN':
        return 'text-cyan-400 bg-cyan-500/20';
      case 'LOGOUT':
        return 'text-gray-400 bg-gray-500/20';
      default:
        return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getEntityTypeColor = (entityType: string) => {
    switch (entityType) {
      case 'Position':
        return 'text-blue-300 bg-blue-500/10';
      case 'Order':
        return 'text-yellow-300 bg-yellow-500/10';
      case 'Deal':
        return 'text-green-300 bg-green-500/10';
      case 'Account':
        return 'text-purple-300 bg-purple-500/10';
      case 'System':
        return 'text-gray-300 bg-gray-500/10';
      default:
        return 'text-gray-300 bg-gray-500/10';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString(),
      precise: date.toISOString()
    };
  };

  const toggleRowExpansion = (auditId: string) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(auditId)) {
        newSet.delete(auditId);
      } else {
        newSet.add(auditId);
      }
      return newSet;
    });
  };

  const filteredAuditLog = useMemo(() => {
    return auditLog.filter(entry => {
      // User filter
      if (filters.userId !== 'all' && entry.userId !== filters.userId) {
        return false;
      }

      // Action filter
      if (filters.action !== 'all' && entry.action !== filters.action) {
        return false;
      }

      // Entity type filter
      if (filters.entityType !== 'all' && entry.entityType !== filters.entityType) {
        return false;
      }

      // Entity ID filter
      if (filters.entityId && !entry.entityId.toLowerCase().includes(filters.entityId.toLowerCase())) {
        return false;
      }

      // Account ID filter
      if (filters.accountId && entry.accountId && !entry.accountId.toLowerCase().includes(filters.accountId.toLowerCase())) {
        return false;
      }

      // Symbol filter
      if (filters.symbol && entry.symbol && !entry.symbol.toLowerCase().includes(filters.symbol.toLowerCase())) {
        return false;
      }

      // Date range filter
      if (filters.dateFrom || filters.dateTo) {
        const entryDate = new Date(entry.timestamp);
        if (filters.dateFrom) {
          const fromDate = new Date(filters.dateFrom);
          if (entryDate < fromDate) return false;
        }
        if (filters.dateTo) {
          const toDate = new Date(filters.dateTo + 'T23:59:59');
          if (entryDate > toDate) return false;
        }
      }

      return true;
    });
  }, [auditLog, filters]);

  const uniqueUsers = [...new Set(auditLog.map(entry => entry.userId))];
  const uniqueActions = [...new Set(auditLog.map(entry => entry.action))];
  const uniqueEntityTypes = [...new Set(auditLog.map(entry => entry.entityType))];

  return (
    <div className="h-full flex flex-col">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-3 flex-shrink-0 mb-4">
        {/* Title and Stats */}
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold text-white">Audit Log</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <Eye className="w-4 h-4" />
            <span>{filteredAuditLog.length} entries</span>
            {filteredAuditLog.length !== auditLog.length && (
              <span className="text-yellow-400">
                (filtered from {auditLog.length})
              </span>
            )}
          </div>
        </div>

        {/* Compact Filters */}
        <div className="flex flex-wrap gap-2">
          <div className="min-w-0 flex-1 lg:flex-none lg:w-24">
            <label className="block text-xs font-medium text-gray-400 mb-1">User</label>
            <select
              value={filters.userId}
              onChange={(e) => setFilters({ ...filters, userId: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="all">All Users</option>
              {uniqueUsers.map(user => (
                <option key={user} value={user}>{user}</option>
              ))}
            </select>
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-24">
            <label className="block text-xs font-medium text-gray-400 mb-1">Action</label>
            <select
              value={filters.action}
              onChange={(e) => setFilters({ ...filters, action: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="all">All Actions</option>
              {uniqueActions.map(action => (
                <option key={action} value={action}>{action}</option>
              ))}
            </select>
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-20">
            <label className="block text-xs font-medium text-gray-400 mb-1">Entity</label>
            <select
              value={filters.entityType}
              onChange={(e) => setFilters({ ...filters, entityType: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              {uniqueEntityTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-24">
            <label className="block text-xs font-medium text-gray-400 mb-1">Entity ID</label>
            <input
              type="text"
              placeholder="POS001..."
              value={filters.entityId}
              onChange={(e) => setFilters({ ...filters, entityId: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-24">
            <label className="block text-xs font-medium text-gray-400 mb-1">Account</label>
            <input
              type="text"
              placeholder="ACC001..."
              value={filters.accountId}
              onChange={(e) => setFilters({ ...filters, accountId: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-28">
            <label className="block text-xs font-medium text-gray-400 mb-1">Date From</label>
            <input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-28">
            <label className="block text-xs font-medium text-gray-400 mb-1">Date To</label>
            <input
              type="date"
              value={filters.dateTo}
              onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Audit Log Table */}
      <div className="flex-1 overflow-hidden">
        {filteredAuditLog.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="w-6 h-6 text-gray-400" />
              </div>
              <p className="text-gray-400 text-lg">No audit entries found.</p>
              <p className="text-gray-500 text-sm mt-2">
                All system actions are logged here with full audit trail details.
              </p>
            </div>
          </div>
        ) : (
          <div className="h-full overflow-y-auto border border-slate-600 rounded-lg">
            <table className="w-full">
              <thead className="sticky top-0 bg-slate-800 z-10">
                <tr className="border-b border-slate-600">
                  <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400 w-8"></th>
                  <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Timestamp</th>
                  <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">User ID</th>
                  <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Action</th>
                  <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Entity</th>
                  <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Entity ID</th>
                  <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Account</th>
                  <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Symbol</th>
                  <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">IP Address</th>
                </tr>
              </thead>
              <tbody>
                {filteredAuditLog.map((entry) => {
                  const timestamp = formatTimestamp(entry.timestamp);
                  const isExpanded = expandedRows.has(entry.id);
                  
                  return (
                    <React.Fragment key={entry.id}>
                      <tr 
                        className="border-b border-slate-700 hover:bg-slate-700/50 cursor-pointer"
                        onClick={() => toggleRowExpansion(entry.id)}
                      >
                        <td className="p-2 sm:p-3">
                          <button className="text-gray-400 hover:text-white transition-colors">
                            {isExpanded ? '▼' : '▶'}
                          </button>
                        </td>
                        <td className="p-2 sm:p-3 text-xs sm:text-sm">
                          <div className="text-white">{timestamp.date}</div>
                          <div className="text-gray-400 text-xs">{timestamp.time}</div>
                        </td>
                        <td className="p-2 sm:p-3 text-xs sm:text-sm">
                          <div className="flex items-center space-x-2">
                            <User className="w-3 h-3 text-gray-400" />
                            <span className="text-white font-medium">{entry.userId}</span>
                          </div>
                        </td>
                        <td className="p-2 sm:p-3 text-xs sm:text-sm">
                          <div className="flex items-center space-x-2">
                            {getActionIcon(entry.action)}
                            <span className={`px-2 py-1 rounded text-xs font-medium ${getActionColor(entry.action)}`}>
                              {entry.action}
                            </span>
                          </div>
                        </td>
                        <td className="p-2 sm:p-3 text-xs sm:text-sm">
                          <span className={`px-2 py-1 rounded text-xs ${getEntityTypeColor(entry.entityType)}`}>
                            {entry.entityType}
                          </span>
                        </td>
                        <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{entry.entityId}</td>
                        <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{entry.accountId || '-'}</td>
                        <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-medium">{entry.symbol || '-'}</td>
                        <td className="p-2 sm:p-3 text-xs sm:text-sm text-gray-400 font-mono">{entry.ipAddress}</td>
                      </tr>
                      
                      {isExpanded && (
                        <tr className="border-b border-slate-700">
                          <td colSpan={9} className="p-0">
                            <div className="bg-slate-700/30 p-4 space-y-3">
                              {/* Detailed Information */}
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <h4 className="text-sm font-medium text-white mb-2">Action Details</h4>
                                  <p className="text-sm text-gray-300 break-words">{entry.details}</p>
                                </div>
                                <div>
                                  <h4 className="text-sm font-medium text-white mb-2">Session Info</h4>
                                  <div className="space-y-1 text-xs">
                                    <div className="flex justify-between">
                                      <span className="text-gray-400">Session ID:</span>
                                      <span className="text-white font-mono">{entry.sessionId}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-400">Precise Time:</span>
                                      <span className="text-white font-mono">{timestamp.precise}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-400">Audit ID:</span>
                                      <span className="text-white font-mono">{entry.id}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              
                              {/* Changes Details */}
                              {entry.changes && entry.changes.length > 0 && (
                                <div>
                                  <h4 className="text-sm font-medium text-white mb-2">Field Changes</h4>
                                  <div className="space-y-2">
                                    {entry.changes.map((change, index) => (
                                      <div key={index} className="bg-slate-600/50 p-2 rounded text-xs">
                                        <div className="flex items-center space-x-2 mb-1">
                                          <Edit className="w-3 h-3 text-blue-400" />
                                          <span className="text-white font-medium">{change.field}</span>
                                        </div>
                                        <div className="flex items-center space-x-4 text-xs">
                                          <div className="flex items-center space-x-1">
                                            <span className="text-gray-400">From:</span>
                                            <span className="text-red-300 font-mono">{String(change.from)}</span>
                                          </div>
                                          <div className="flex items-center space-x-1">
                                            <span className="text-gray-400">To:</span>
                                            <span className="text-green-300 font-mono">{String(change.to)}</span>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuditLogTab; 