import React, { useState, useEffect } from 'react';
import Header from './Header';
import DealerContextPanel from './DealerContextPanel';
import TraderInfoPanel from './TraderInfoPanel';
import PriceQuotesPanel from './PriceQuotesPanel';
import MainDataPanel from './MainDataPanel';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { Building2, ExternalLink, X } from 'lucide-react';

const Dashboard = () => {
  const navigate = useNavigate();
  const [showDemoBanner, setShowDemoBanner] = useState(false);
  const [brokerInfo, setBrokerInfo] = useState<any>(null);

  useEffect(() => {
    // Check if this is a demo session from broker onboarding
    const brokerData = localStorage.getItem('brokerOnboarding');
    if (brokerData) {
      const data = JSON.parse(brokerData);
      if (data.status === 'configured') {
        setShowDemoBanner(true);
        setBrokerInfo(data);
      }
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Demo Banner */}
      {showDemoBanner && (
        <div className="bg-gradient-to-r from-blue-600 to-cyan-600 border-b border-blue-500">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Building2 className="w-3 h-3 mr-1" />
                  Demo Mode
                </Badge>
                <div>
                  <p className="text-white font-medium">
                    Welcome to {brokerInfo?.configuration?.companyName || 'Your'} OTX Dealer Terminal Demo
                  </p>
                  <p className="text-blue-100 text-sm">
                    You're viewing a live demo with configured symbol groups.
                    <Button
                      variant="link"
                      className="text-blue-100 underline p-0 h-auto ml-1"
                      onClick={() => navigate('/broker/dashboard')}
                    >
                      Return to Broker Dashboard
                      <ExternalLink className="w-3 h-3 ml-1" />
                    </Button>
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDemoBanner(false)}
                className="text-white hover:bg-white/20"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      <Header />
      <div className="p-2 sm:p-4 space-y-2 sm:space-y-4">
        {/* First Row: DealerContext + TraderInfo - Fixed height for internal scrolling */}
        <div className="grid grid-cols-1 xl:grid-cols-5 gap-2 sm:gap-4">
          <div className="xl:col-span-1">
            <div className="h-80">
              <DealerContextPanel />
            </div>
          </div>
          <div className="xl:col-span-4">
            <div className="h-80">
              <TraderInfoPanel />
            </div>
          </div>
        </div>
        
        {/* Second Row: PriceQuotes + MainData - Fixed height for internal scrolling */}
        <div className="grid grid-cols-1 xl:grid-cols-5 gap-2 sm:gap-4">
          <div className="xl:col-span-1">
            <div className="h-[500px]">
              <PriceQuotesPanel />
            </div>
          </div>
          <div className="xl:col-span-4">
            <div className="h-[500px]">
              <MainDataPanel />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
