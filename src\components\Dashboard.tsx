import React, { useState } from 'react';
import Header from './Header';
import DealerContextPanel from './DealerContextPanel';
import TraderInfoPanel from './TraderInfoPanel';
import PriceQuotesPanel from './PriceQuotesPanel';
import MainDataPanel from './MainDataPanel';

const Dashboard = () => {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Header />
      <div className="p-2 sm:p-4 space-y-2 sm:space-y-4">
        {/* First Row: DealerContext + TraderInfo - Fixed height for internal scrolling */}
        <div className="grid grid-cols-1 xl:grid-cols-5 gap-2 sm:gap-4">
          <div className="xl:col-span-1">
            <div className="h-80">
              <DealerContextPanel />
            </div>
          </div>
          <div className="xl:col-span-4">
            <div className="h-80">
              <TraderInfoPanel />
            </div>
          </div>
        </div>
        
        {/* Second Row: PriceQuotes + MainData - Fixed height for internal scrolling */}
        <div className="grid grid-cols-1 xl:grid-cols-5 gap-2 sm:gap-4">
          <div className="xl:col-span-1">
            <div className="h-[500px]">
              <PriceQuotesPanel />
            </div>
          </div>
          <div className="xl:col-span-4">
            <div className="h-[500px]">
              <MainDataPanel />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
