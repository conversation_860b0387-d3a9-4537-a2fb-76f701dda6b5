import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  ExternalLink, 
  Users, 
  Monitor, 
  Smartphone,
  Settings,
  BarChart3,
  Shield,
  Clock,
  Building2,
  Globe,
  ArrowRight,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react';

interface BrokerData {
  billingInfo: any;
  selectedPlan: any;
  configuration: any;
  paymentDate: string;
  configurationDate: string;
  subscriptionId: string;
  status: string;
}

const BrokerDashboard = () => {
  const navigate = useNavigate();
  const [brokerData, setBrokerData] = useState<BrokerData | null>(null);
  const [showCredentials, setShowCredentials] = useState(false);

  useEffect(() => {
    const data = localStorage.getItem('brokerOnboarding');
    if (!data) {
      navigate('/broker');
      return;
    }
    
    const parsedData = JSON.parse(data);
    if (parsedData.status !== 'configured') {
      navigate('/broker/configuration');
      return;
    }
    
    setBrokerData(parsedData);
  }, [navigate]);

  if (!brokerData) {
    return <div>Loading...</div>;
  }

  const setupProgress = 100; // Configuration is complete
  const companyName = brokerData.configuration.companyName;
  const selectedSymbolGroups = Object.entries(brokerData.configuration.symbolGroups)
    .filter(([_, selected]) => selected)
    .map(([key, _]) => key);

  // Generate demo credentials
  const demoCredentials = {
    crmAdmin: {
      username: 'admin@' + companyName.toLowerCase().replace(/\s+/g, '') + '.com',
      password: 'Demo123!',
      url: 'https://crm.otxplatform.com'
    },
    dealerTerminal: {
      username: 'dealer@' + companyName.toLowerCase().replace(/\s+/g, '') + '.com',
      password: 'Dealer123!',
      url: '/'
    }
  };

  const quickStartSteps = [
    {
      title: 'Access CRM Admin Portal',
      description: 'Set up your client management system and configure user groups',
      icon: <Users className="w-5 h-5" />,
      completed: true,
      action: 'Open CRM'
    },
    {
      title: 'Configure Dealer Terminal',
      description: 'Set up trading parameters and risk management settings',
      icon: <Monitor className="w-5 h-5" />,
      completed: true,
      action: 'Open Terminal'
    },
    {
      title: 'Customize Mobile App',
      description: 'Brand your mobile trading app with company logo and colors',
      icon: <Smartphone className="w-5 h-5" />,
      completed: false,
      action: 'Customize'
    },
    {
      title: 'Set Up Payment Gateway',
      description: 'Configure payment processing for client deposits and withdrawals',
      icon: <Shield className="w-5 h-5" />,
      completed: false,
      action: 'Configure'
    }
  ];

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Header */}
      <header className="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">OTX</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">{companyName} Dashboard</h1>
                <p className="text-sm text-gray-400">Your trading platform is ready!</p>
              </div>
            </div>
            <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
              <CheckCircle className="w-3 h-3 mr-1" />
              Platform Active
            </Badge>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <div className="text-center mb-6">
            <h2 className="text-4xl font-bold text-white mb-2">
              🎉 Welcome to Your Trading Platform!
            </h2>
            <p className="text-xl text-gray-300">
              Congratulations! Your complete trading infrastructure is now live and ready for business.
            </p>
          </div>

          {/* Setup Progress */}
          <Card className="bg-slate-800/50 border-slate-700 mb-6">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                Platform Setup Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <Progress value={setupProgress} className="flex-1" />
                <span className="text-white font-medium">{setupProgress}%</span>
              </div>
              <p className="text-sm text-gray-400 mt-2">
                Your platform configuration is complete. You can now access all components.
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Access Panel */}
          <div className="lg:col-span-2 space-y-6">
            {/* Platform Access Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* CRM Admin Portal */}
              <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Users className="w-6 h-6 mr-3 text-blue-400" />
                    CRM Admin Portal
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    Manage clients, leads, and business operations
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-400">Username:</span>
                      <div className="flex items-center space-x-2">
                        <code className="text-xs bg-slate-700 px-2 py-1 rounded text-white">
                          {showCredentials ? demoCredentials.crmAdmin.username : '••••••••••••'}
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(demoCredentials.crmAdmin.username)}
                          className="h-6 w-6 p-0"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-400">Password:</span>
                      <div className="flex items-center space-x-2">
                        <code className="text-xs bg-slate-700 px-2 py-1 rounded text-white">
                          {showCredentials ? demoCredentials.crmAdmin.password : '••••••••'}
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(demoCredentials.crmAdmin.password)}
                          className="h-6 w-6 p-0"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  <Button 
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    onClick={() => window.open(demoCredentials.crmAdmin.url, '_blank')}
                  >
                    Access CRM Portal
                    <ExternalLink className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>

              {/* Dealer Terminal */}
              <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Monitor className="w-6 h-6 mr-3 text-green-400" />
                    Dealer Terminal
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    Professional trading and risk management
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-400">Username:</span>
                      <div className="flex items-center space-x-2">
                        <code className="text-xs bg-slate-700 px-2 py-1 rounded text-white">
                          {showCredentials ? demoCredentials.dealerTerminal.username : '••••••••••••'}
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(demoCredentials.dealerTerminal.username)}
                          className="h-6 w-6 p-0"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-400">Password:</span>
                      <div className="flex items-center space-x-2">
                        <code className="text-xs bg-slate-700 px-2 py-1 rounded text-white">
                          {showCredentials ? demoCredentials.dealerTerminal.password : '••••••••'}
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(demoCredentials.dealerTerminal.password)}
                          className="h-6 w-6 p-0"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  <Button 
                    className="w-full bg-green-600 hover:bg-green-700"
                    onClick={() => navigate('/')}
                  >
                    Access Dealer Terminal
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Credentials Toggle */}
            <div className="flex justify-center">
              <Button
                variant="outline"
                onClick={() => setShowCredentials(!showCredentials)}
                className="border-slate-600 text-white hover:bg-slate-800"
              >
                {showCredentials ? (
                  <>
                    <EyeOff className="w-4 h-4 mr-2" />
                    Hide Credentials
                  </>
                ) : (
                  <>
                    <Eye className="w-4 h-4 mr-2" />
                    Show Credentials
                  </>
                )}
              </Button>
            </div>

            {/* Quick Start Guide */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Quick Start Guide
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Complete these steps to get your brokerage fully operational
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {quickStartSteps.map((step, index) => (
                    <div key={index} className="flex items-center space-x-4 p-3 rounded-lg bg-slate-700/30">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        step.completed ? 'bg-green-500/20 text-green-400' : 'bg-slate-600 text-gray-400'
                      }`}>
                        {step.completed ? <CheckCircle className="w-4 h-4" /> : step.icon}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-white">{step.title}</h3>
                        <p className="text-sm text-gray-400">{step.description}</p>
                      </div>
                      <Button
                        size="sm"
                        variant={step.completed ? "default" : "outline"}
                        className={step.completed ? "bg-green-600 hover:bg-green-700" : "border-slate-600"}
                      >
                        {step.action}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Configuration Summary */}
          <div className="space-y-6">
            {/* Company Info */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Building2 className="w-5 h-5 mr-2" />
                  Company Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="text-sm text-gray-400">Company Name:</span>
                  <p className="text-white font-medium">{companyName}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-400">Contact Email:</span>
                  <p className="text-white">{brokerData.configuration.contactEmail}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-400">Plan:</span>
                  <p className="text-white">{brokerData.selectedPlan.name}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-400">Subscription ID:</span>
                  <p className="text-xs text-gray-300 font-mono">{brokerData.subscriptionId}</p>
                </div>
              </CardContent>
            </Card>

            {/* Trading Configuration */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Settings className="w-5 h-5 mr-2" />
                  Trading Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="text-sm text-gray-400">Max Client Accounts:</span>
                  <p className="text-white">{brokerData.configuration.userGroups}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-400">Demo Accounts:</span>
                  <p className="text-white">{brokerData.configuration.traderAccounts}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-400">Symbol Groups:</span>
                  <p className="text-white">{selectedSymbolGroups.length} groups selected</p>
                </div>
              </CardContent>
            </Card>

            {/* Support */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Shield className="w-5 h-5 mr-2" />
                  Support & Resources
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-800">
                  <Globe className="w-4 h-4 mr-2" />
                  Documentation
                </Button>
                <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-800">
                  <Users className="w-4 h-4 mr-2" />
                  Contact Support
                </Button>
                <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-800">
                  <Clock className="w-4 h-4 mr-2" />
                  Schedule Training
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrokerDashboard;
