# OTX Dealer Platform User Manual

**Version**: 1.0  
**Publication Date**: 2025-07-30

## Table of Contents

1. [Introduction](#1-introduction)
   - 1.1 [Welcome](#11-welcome)
   - 1.2 [About this Platform](#12-about-this-platform)
   - 1.3 [How to Use this Manual](#13-how-to-use-this-manual)

2. [Getting Started](#2-getting-started)
   - 2.1 [System Requirements](#21-system-requirements)
   - 2.2 [Account Registration & Login](#22-account-registration--login)
   - 2.3 [Dashboard Overview](#23-dashboard-overview)

3. [Core Features](#3-core-features)
   - 3.1 [Product Catalog](#31-product-catalog)
   - 3.2 [Order Management](#32-order-management)
   - 3.3 [Account Management](#33-account-management)
   - 3.4 [Finance Center](#34-finance-center)
   - 3.5 [Resource Center](#35-resource-center)

4. [Frequently Asked Questions (FAQ)](#4-frequently-asked-questions-faq)

5. [Support & Contact Information](#5-support--contact-information)

6. [Appendix](#6-appendix)

---

## 1. Introduction

### 1.1 Welcome

Welcome to the OTX Dealer Platform - your professional gateway to comprehensive financial trading operations. This platform has been designed specifically for business dealers who require reliable, real-time access to global markets with institutional-grade tools and features.

Whether you're managing client portfolios, executing complex trading strategies, or monitoring market positions across multiple instruments, the OTX Dealer Platform provides the professional infrastructure you need to operate efficiently in today's fast-paced financial markets.

### 1.2 About this Platform

The OTX Dealer Platform is a comprehensive web-based trading terminal that enables 24/7 market access and portfolio management. The platform's core value propositions include:

**Real-Time Market Access**
- Live pricing feeds for forex, commodities, and cryptocurrencies
- Instant order execution with professional-grade trading tools
- Multi-instrument portfolio management capabilities

**Professional Trading Tools**
- Advanced position and order management system
- Comprehensive risk management features with automated controls
- Real-time profit/loss calculations and account monitoring

**Compliance and Audit Features**
- Complete audit trail for all trading activities
- Regulatory-compliant record keeping and reporting
- Multi-level user access controls and permissions

**Account Management**
- Multi-account oversight for client portfolio management
- Detailed transaction history and performance analytics
- Integrated margin monitoring and risk alerts

### 1.3 How to Use this Manual

This manual is structured to guide you from initial setup through advanced platform features. Each section builds upon previous knowledge, making it easy to reference specific procedures or learn the platform systematically.

**Navigation Structure**
- **Getting Started**: Essential setup and initial orientation
- **Core Features**: Detailed instructions for primary platform functions
- **FAQ**: Quick answers to common questions
- **Support**: Contact information for technical assistance

**Icons and Indicators**
Throughout this manual, you'll find these helpful indicators:

💡 **Tip**: Helpful shortcuts, best practices, and efficiency recommendations

⚠️ **Warning**: Critical information that requires careful attention to prevent errors or security issues

**Screenshot References**
Key procedures include screenshot placeholders marked as [Insert screenshot of...]. These indicate where visual references would help clarify the instructions in a live environment.

---

## 2. Getting Started

### 2.1 System Requirements

For optimal performance with the OTX Dealer Platform, ensure your system meets these minimum requirements:

**Supported Web Browsers**
- Google Chrome (version 90 or higher)
- Mozilla Firefox (version 88 or higher)
- Safari (version 14 or higher)
- Microsoft Edge (version 90 or higher)

**System Specifications**
- RAM: 8GB minimum (16GB recommended for optimal performance)
- Screen Resolution: 1920x1080 minimum for full interface visibility
- Internet Connection: Stable broadband connection (10Mbps minimum)
- Operating System: Windows 10+, macOS 10.15+, or modern Linux distribution

💡 **Tip**: For professional trading environments, consider using multiple monitors to maximize workspace efficiency and enable simultaneous monitoring of different market data panels.

### 2.2 Account Registration & Login

Access to the OTX Dealer Platform requires authorized dealer credentials provided by your organization's system administrator.

**Accessing the Platform**
1. Navigate to your organization's platform URL: [Enter your platform URL here]
2. You will be directed to the secure login interface

**Login Process**
1. Enter your assigned **Username** in the first field
2. Enter your **Password** in the second field
3. Complete the **CAPTCHA verification** by checking the "I'm not a robot" box
4. Follow any additional CAPTCHA instructions if prompted
5. Click the **"Login"** button to access the platform

[Insert screenshot of the login page, highlighting the username, password, CAPTCHA, and login button]

**Demo Access**
For demonstration and training purposes, you may use these credentials:
- **Username**: dealer01
- **Password**: password123

⚠️ **Warning**: Always complete the CAPTCHA verification before attempting to log in. The system will reject login attempts without proper CAPTCHA completion.

**Forgot Password Functionality**
If you cannot access your account:
1. Contact your system administrator for password reset assistance
2. Provide your username and any required verification information
3. Follow the password reset procedure provided by your organization

### 2.3 Dashboard Overview

Upon successful login, you'll access the main trading dashboard featuring a professional 4-panel layout optimized for efficient market operations.

[Insert screenshot of the main dashboard, highlighting key areas]

**Main Interface Layout**

The dashboard is organized into four primary panels:

**Top Row Panels**
- **Dealer Analytics Panel** (Top-Left): Performance metrics, alerts, and risk monitoring
- **Trader Info Panel** (Top-Right): Account information, balances, and margin levels

**Bottom Row Panels**  
- **Price Quotes Panel** (Bottom-Left): Real-time market data and trading controls
- **Main Data Panel** (Bottom-Right): Tabbed interface for positions, orders, and history

**Key Navigation Elements**

**Main Navigation Bar**
Located at the top of the screen, providing:
- Platform branding and status indicators
- User account information and logout option
- System connection status (green indicates active connection)
- Current session time and market status

**Quick Summary Widgets**
Each panel header displays:
- Panel title and current data summary
- Real-time update indicators
- Quick action buttons for common operations

**User Menu**
Accessible from the top-right corner:
- Account settings and preferences
- User profile information
- Session management options
- Platform logout function

**Search Bar**
Located within the Price Quotes panel:  
- Symbol search functionality
- Quick access to specific trading instruments
- Filter options for instrument categories

💡 **Tip**: The dashboard automatically refreshes real-time data. Monitor the connection indicator to ensure you're receiving live market updates.

---

## 3. Core Features

### 3.1 Product Catalog

The OTX Dealer Platform provides access to a comprehensive range of financial instruments through an intuitive market data interface.

#### 3.1.1 Browsing & Searching for Products

**Accessing the Product Catalog**
The Price Quotes Panel serves as your primary product catalog, displaying all available trading instruments with real-time pricing.

**Search Functionality**
1. Locate the search area within the Price Quotes Panel
2. Enter the symbol name or partial name (e.g., "EUR" for Euro pairs)
3. Use the category filters to narrow results by instrument type
4. Select from the filtered results to view detailed information

[Insert screenshot of the product catalog page]

**Available Instrument Categories**
- **Forex Pairs**: Major, minor, and exotic currency pairs
- **Commodities**: Precious metals (Gold, Silver) and energy products
- **Cryptocurrencies**: Bitcoin, Ethereum, and other digital currencies

**Search Tips**
💡 **Tip**: Use partial symbol names for quick searches. For example, typing "USD" will show all USD-related pairs.

#### 3.1.2 Viewing Product Details & Stock Levels

**Real-Time Market Information**
Each instrument displays comprehensive market data:

**Price Information**
- **Bid Price**: Current buying price
- **Ask Price**: Current selling price  
- **Spread**: Difference between bid and ask prices
- **Price Change**: Movement since previous session

**Instrument Specifications**
- **Symbol Name**: Standard market identifier
- **Precision**: Decimal places for price display
- **Minimum Volume**: Smallest tradeable position size
- **Maximum Volume**: Largest allowable single position

**Market Status Indicators**
- **Green Circle**: Trading enabled and market active
- **Red Circle**: Trading disabled or market closed
- **Yellow Circle**: Limited trading or market warning

**Stock Level Information**
💡 **Tip**: The platform provides real-time liquidity information. Green indicators show normal market conditions with full liquidity available.

### 3.2 Order Management

The OTX Dealer Platform offers comprehensive order management capabilities for both immediate market execution and pending order strategies.

#### 3.2.1 How to Create a New Order

**Method 1: Quick Order from Price Quotes**
1. Locate the desired instrument in the Price Quotes Panel
2. Double-click on the symbol row
3. The New Position modal opens with the symbol pre-selected
4. Configure your order parameters (see steps below)
5. Click "Create Position" to execute

**Method 2: Manual Order Creation**
1. Press **Ctrl+N** on your keyboard, or
2. Right-click in the Main Data Panel and select "New Position"
3. The New Position modal opens for manual configuration

**Order Configuration Steps**
1. **Select Symbol**: Choose trading instrument from dropdown menu
2. **Choose Action**: Select BUY (long position) or SELL (short position)
3. **Enter Volume**: Specify position size in lots (e.g., 0.01, 0.10, 1.00)
4. **Order Type**: Select MARKET for immediate execution
5. **Set Stop Loss**: (Optional) Enter risk management price level  
6. **Set Take Profit**: (Optional) Enter profit target price level
7. **Add Comment**: (Optional) Include notes for record keeping
8. **Execute Order**: Click "Create Position" to submit

[Insert screenshot of the checkout process]

💡 **Tip**: Use the Tab key to navigate quickly between form fields. The platform calculates required margin automatically as you enter position details.

⚠️ **Warning**: Always verify your order details before clicking "Create Position." Market orders execute immediately at current market prices.

#### 3.2.2 Viewing Order History

**Accessing Order History**
1. Navigate to the Main Data Panel (bottom-right)
2. Click on the **"Order History"** tab
3. All historical order activity displays in chronological order

**Order History Information**
- **Order ID**: Unique identifier for each order
- **Symbol**: Trading instrument
- **Action**: BUY or SELL direction
- **Volume**: Order size in lots
- **Order Type**: MARKET, LIMIT, or STOP
- **Price**: Requested execution price
- **Status**: Current order status
- **Time**: Order creation timestamp
- **Comment**: Associated notes

**Filtering Options**
Use the filter controls to narrow results:
- **Date Range**: Select specific time periods
- **Symbol Filter**: Show orders for specific instruments
- **Status Filter**: Display orders by current status
- **Account Filter**: Filter by specific trading accounts

#### 3.2.3 Tracking Order Status

**Order Status Definitions**
- **PENDING**: Order awaiting market conditions for execution
- **FILLED**: Order successfully executed and converted to position
- **CANCELED**: Order manually canceled before execution
- **EXPIRED**: Order expired based on time limitations
- **REJECTED**: Order declined due to insufficient funds or restrictions

**Real-Time Status Updates**
- Order status updates automatically without page refresh
- Visual indicators show status changes with color coding
- Audio alerts (if enabled) notify of important status changes

**Status Monitoring Tips**
💡 **Tip**: Pending orders appear in both the "Orders" tab (active) and "Order History" tab (complete record). Monitor the "Orders" tab for real-time pending order management.

#### 3.2.4 Downloading Invoices & Receipts

**Accessing Financial Documents**
1. Navigate to the **"Deal History"** tab in the Main Data Panel
2. Locate the completed transaction record
3. Right-click on the deal entry
4. Select "Download Receipt" or "Export Deal Details"
5. Choose your preferred file format (PDF recommended)
6. The document downloads to your default download folder

**Available Financial Documents**
- **Deal Receipts**: Individual transaction confirmations
- **Account Statements**: Comprehensive account activity summaries
- **P/L Reports**: Profit/loss analysis for specified periods
- **Tax Documents**: Year-end reporting materials (if applicable)

💡 **Tip**: Download important financial documents regularly for your records. These documents serve as official confirmation of your trading activities.

### 3.3 Account Management

The OTX Dealer Platform provides comprehensive account management tools through the Trader Info Panel and user profile settings.

#### 3.3.1 Updating Your Profile

**Accessing Profile Settings**
1. Click on your username in the top-right corner of the screen
2. Select "Profile Settings" from the dropdown menu
3. The profile management interface opens

**Editable Profile Information**
- **Display Name**: Your name as shown in the platform
- **Company Information**: Organization details and department
- **Contact Details**: Email and phone number for account notifications
- **Time Zone**: Local time zone for timestamp display
- **Language Preferences**: Interface language selection
- **Notification Settings**: Alert preferences for trading activities

**Updating Process**
1. Click on any editable field to modify information
2. Enter the new information
3. Click "Save Changes" to apply updates
4. Confirm changes in the verification dialog

⚠️ **Warning**: Some profile changes may require administrator approval before taking effect. Contact your system administrator for changes to critical account information.

#### 3.3.2 Changing Your Password

**Password Change Process**
1. Access the User Menu from the top-right corner
2. Select "Security Settings"
3. Click "Change Password"
4. Complete the password change form:
   - Enter your current password
   - Enter your new password
   - Confirm your new password
5. Click "Update Password" to save changes

**Password Requirements**
- Minimum 8 characters in length
- Must include uppercase and lowercase letters
- Must contain at least one number
- Special characters recommended for enhanced security

💡 **Tip**: Choose a strong, unique password that you don't use for other systems. Consider using a password manager for secure password storage.

#### 3.3.3 Managing Shipping Addresses

**Note**: The OTX Dealer Platform is a digital trading platform and does not require physical shipping addresses for standard operations. However, you may need to maintain contact addresses for:

**Address Management Functions**
- **Correspondence Address**: For official account statements and regulatory documents
- **Billing Address**: For account fees and commission statements  
- **Emergency Contact**: Alternative contact location for urgent communications

**Managing Contact Addresses**
1. Navigate to Profile Settings
2. Select the "Contact Information" section
3. Click "Edit" next to the address you wish to modify
4. Update the address fields as needed
5. Save changes and verify the information is correct

### 3.4 Finance Center

The Finance Center functionality is integrated throughout the platform, primarily accessible through the Trader Info Panel and various financial reporting tabs.

**Account Balance Overview**
The Trader Info Panel displays real-time financial information:
- **Account Balance**: Total funds in your trading account
- **Equity**: Current account value including unrealized profit/loss
- **Margin**: Funds allocated to maintain open positions
- **Free Margin**: Available funds for new positions
- **Margin Level**: Percentage showing account health

**Viewing Account Statements**
1. Navigate to the Main Data Panel
2. Select the "Deal History" tab for comprehensive transaction records
3. Use date filters to generate statements for specific periods
4. Export statements using the "Export" button for external record keeping

**Outstanding Balances and Credit Limits**
- **Margin Requirements**: Automatically calculated for each position
- **Credit Utilization**: Displayed as margin level percentage
- **Available Credit**: Shown as free margin amount
- **Risk Alerts**: Automatic warnings when approaching margin limits

💡 **Tip**: Monitor your margin level regularly. Levels below 100% indicate potential margin call situations requiring immediate attention.

### 3.5 Resource Center

The Resource Center provides access to comprehensive platform resources and documentation through integrated features and data management tools.

**Accessing Marketing Materials and Documentation**
- **User Manual**: This comprehensive guide accessible through the Help menu
- **Platform Updates**: Notifications and changelog information
- **Market Analysis**: Available through external links (if provided by your organization)
- **Training Resources**: Video tutorials and best practices guides

**Downloading Specification Sheets**
1. Right-click on any symbol in the Price Quotes Panel
2. Select "Symbol Information" from the context menu
3. View detailed instrument specifications including:
   - Contract specifications
   - Trading hours
   - Margin requirements  
   - Commission structures

**Company Announcements**
- **System Notifications**: Important platform updates appear in the header area
- **Market Notices**: Trading hour changes and market closures
- **Maintenance Alerts**: Scheduled downtime and system updates
- **Regulatory Updates**: Compliance and policy changes

💡 **Tip**: Check the announcements section regularly to stay informed about platform updates, market changes, and important notices that may affect your trading activities.

---

## 4. Frequently Asked Questions (FAQ)

### Q1: What should I do if I can't log in to the platform?

**Answer**: Login issues are typically caused by one of several common factors:
1. **Verify CAPTCHA completion** - Ensure you've checked the "I'm not a robot" box and completed any additional verification
2. **Check credentials** - Confirm your username and password are entered correctly, including proper capitalization
3. **Clear browser cache** - Remove stored cookies and cache data, then try logging in again
4. **Try a different browser** - Test with Chrome, Firefox, or Edge to eliminate browser-specific issues
5. **Contact your administrator** - If problems persist, your account may need to be reset or unlocked

### Q2: How do I change an order after placing it?

**Answer**: Order modification depends on the order type and current status:
- **Market Orders**: Cannot be modified once executed as they become positions immediately
- **Pending Orders**: Can be modified while in PENDING status:
  1. Navigate to the "Orders" tab in the Main Data Panel
  2. Double-click the pending order to open the modification dialog
  3. Adjust price, volume, or risk parameters as needed
  4. Click "Update Order" to save changes
- **Executed Orders**: Become positions and must be managed through position modification tools

💡 **Tip**: Pending orders can only be modified before execution. Once filled, they become positions that require different management procedures.

### Q3: Where can I find my dealer-specific pricing?

**Answer**: Dealer-specific pricing is integrated throughout the platform:
- **Real-time Prices**: Displayed in the Price Quotes Panel with your specific spreads and commissions applied
- **Position Calculations**: All profit/loss calculations use your account's specific pricing structure
- **Historical Data**: Past transactions reflect the actual prices and fees charged to your account
- **Account Information**: Your user group (Premium/Standard) determines pricing tier and is shown in the Trader Info Panel

The platform automatically applies your dealer-specific rates to all trading activities, so displayed prices reflect your actual trading costs.

### Q4: How do I monitor my risk exposure across all positions?

**Answer**: The platform provides comprehensive risk monitoring tools:
- **Dealer Analytics Panel**: Shows aggregate profit/loss and risk metrics across all positions
- **Trader Info Panel**: Displays margin level, free margin, and account equity in real-time
- **Positions Tab**: Lists all open positions with individual and total P/L calculations
- **Risk Alerts**: Automatic warnings appear when approaching margin call levels (typically below 100% margin level)

⚠️ **Warning**: Monitor your margin level closely. Levels below 50% may trigger automatic position closures to protect your account.

### Q5: What happens if my internet connection is lost during trading?

**Answer**: The platform includes several protective measures for connection issues:
- **Automatic Reconnection**: The system attempts to reconnect automatically when connection is restored
- **Connection Status Indicator**: Red indicator shows disconnection, green shows active connection
- **Stop Loss/Take Profit**: These risk management orders remain active on the server even during disconnection
- **Position Protection**: Existing positions remain open and are not affected by temporary connection loss

When connection is restored, refresh your browser page to ensure you're seeing current market data and position status. If you experience frequent disconnections, contact technical support for network troubleshooting assistance.

---

## 5. Support & Contact Information

For technical assistance, trading support, or account-related inquiries, please use the appropriate contact method below:

**Customer Support Hotline**: [Enter Phone Number]  
Available for immediate assistance with platform issues, account problems, and urgent trading support.

**Technical Support Email**: [Enter Support Email]  
For non-urgent technical issues, bug reports, and feature requests. Please include detailed information about your issue and browser/system information.

**Business Hours**: [Enter Business Hours, including time zone]  
Support availability may vary by service type. Emergency trading support may be available outside normal business hours.

**Emergency Contact**: For critical issues affecting trading operations or account security, use the emergency contact procedures provided by your organization.

💡 **Tip**: When contacting support, have your account information, browser details, and a clear description of the issue ready to expedite assistance.

---

## 6. Appendix

### Glossary of Terms

**Ask Price**: The price at which the market is willing to sell a financial instrument. Also known as the "offer price."

**Audit Log**: A comprehensive record of all user activities and system events for compliance and security monitoring.

**Balance**: The total amount of funds in a trading account, not including unrealized profits or losses from open positions.

**Bid Price**: The price at which the market is willing to buy a financial instrument.

**CAPTCHA**: A security measure that requires users to complete a verification task to prove they are human and not automated bots.

**Dashboard**: The main interface screen showing all key trading panels and real-time market information.

**Deal**: A completed trading transaction, including both the opening and closing of a position.

**Equity**: The current value of a trading account, including the account balance plus any unrealized profits or losses from open positions.

**Free Margin**: The amount of funds available in an account to open new positions after accounting for margin requirements of existing positions.

**Leverage**: The ratio of the total position size to the margin required to maintain that position.

**Limit Order**: An order to buy or sell at a specific price or better. The order will only execute at the specified price or more favorable.

**Lot**: The standard unit of trading for financial instruments. For forex, one standard lot typically equals 100,000 units of the base currency.

**Margin**: The required deposit to open and maintain a trading position. It acts as a good faith deposit for the leveraged position.

**Margin Call**: A warning that occurs when account equity falls below the required margin level, requiring additional funds or position closure.

**Margin Level**: The percentage ratio of equity to used margin, calculated as (Equity ÷ Used Margin) × 100.

**Market Order**: An order to buy or sell immediately at the best available current market price.

**MOQ (Minimum Order Quantity)**: The smallest position size that can be traded for a specific financial instrument.

**P/L (Profit/Loss)**: The amount gained or lost on a trading position, either realized (closed positions) or unrealized (open positions).

**Pending Order**: An order that has been placed but not yet executed, waiting for specific market conditions to be met.

**Position**: An open trade that will gain or lose value based on market price movements until it is closed.

**SKU (Stock Keeping Unit)**: In trading contexts, refers to the unique identifier for financial instruments and trading products.

**Spread**: The difference between the bid price and ask price of a financial instrument.

**Stop Loss**: An order placed to automatically close a position when it reaches a predetermined loss level, limiting potential losses.

**Stop Order**: An order that becomes a market order when a specified price level is reached.

**Stop Out**: Automatic closure of positions when account equity falls below the minimum required level to maintain open positions.

**Swap**: The interest rate differential between two currencies in a forex position, charged or credited for positions held overnight.

**Symbol**: The standardized code used to identify a specific financial instrument (e.g., EUR/USD, XAUUSD).

**Take Profit**: An order placed to automatically close a position when it reaches a predetermined profit level.

**Used Margin**: The total amount of margin currently allocated to maintain all open positions.

**Volume**: The size of a trading position, typically measured in lots or units of the base currency.

**WebSocket**: The technology used to provide real-time data updates between the trading platform and the server.

---

*This manual is current as of Version 1.0 dated 2025-07-30. For the most up-to-date information and additional resources, please contact your support team or system administrator.*