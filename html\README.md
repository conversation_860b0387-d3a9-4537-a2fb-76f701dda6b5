# OTX Platform - Static HTML Broker Demo

A complete standalone HTML demo of the broker onboarding flow that can be easily customized by changing company names, pricing, and other details without touching the existing React project.

## 📁 Structure

```
html/
├── index.html          # Landing/marketing page
├── payment.html        # Payment and billing page
├── configuration.html  # 3-step platform setup wizard
├── dashboard.html      # Final dashboard with access credentials
├── assets/
│   ├── config.js      # 🎯 Main configuration file (EDIT THIS!)
│   ├── styles.css     # Unified styling
│   └── script.js      # Interactive functionality
└── README.md          # This file
```

## 🚀 Quick Start

1. **Open the demo**: Open `index.html` in your web browser
2. **Customize content**: Edit `assets/config.js` to change company details
3. **Test the flow**: Navigate through all 4 pages of the broker onboarding process

## 🎯 Easy Customization

### Primary Configuration File: `assets/config.js`

This single file contains all customizable content. Simply edit the values to rebrand the entire demo:

#### Company Branding
```javascript
company: {
    name: "Your Company Name",        // Changes throughout all pages
    tagline: "Your Company Tagline",  // Appears in headers
    logo: "YCN",                      // Text logo or path to image
    domain: "yourcompany.com",        // Used for email generation
    year: "2024"                      // Copyright year
}
```

#### Pricing Plans
```javascript
plans: [
    {
        name: "Starter",
        price: "$2,999",             // Monthly price
        period: "/month",
        description: "Perfect for new brokers...",
        popular: false,             // Shows "Most Popular" badge
        features: [
            "Up to 100 client accounts",
            "Basic CRM functionality",
            // ... more features
        ]
    }
    // ... more plans
]
```

#### Features Section
```javascript
features: [
    {
        icon: "👥",                  // Emoji or icon
        title: "Complete CRM System",
        description: "Manage clients, leads..."
    }
    // ... 6 features total
]
```

#### Platform Access & Credentials
```javascript
platforms: {
    crm: {
        name: "CRM Admin Portal",
        url: "https://your-crm-url.com",
        username: "admin@{company}.com",  // {company} auto-replaced
        password: "Demo123!"
    },
    dealer: {
        name: "Dealer Terminal", 
        url: "https://your-dealer-url.com",
        username: "dealer@{company}.com",
        password: "Dealer123!"
    }
}
```

#### Support & External Links
```javascript
support: {
    email: "<EMAIL>",
    documentation: "https://docs.yourcompany.com",
    training: "https://calendly.com/yourcompany/training",
    phone: "+****************"
}
```

### Color Theme Customization

Edit CSS variables in `assets/styles.css`:

```css
:root {
    --primary-blue: #3b82f6;        /* Primary button color */
    --primary-cyan: #06b6d4;        /* Primary gradient end */
    --slate-900: #0f172a;           /* Dark background */
    /* ... more colors */
}
```

## 📄 Page Flow

### 1. Landing Page (`index.html`)
- **Hero section** with customizable company branding
- **6 feature cards** with icons and descriptions  
- **3 pricing tiers** with "Most Popular" badges
- **Call-to-action sections**
- **Navigation** to payment flow

### 2. Payment Page (`payment.html`)
- **Order summary** with selected plan details
- **Complete billing forms** (personal, payment, address)
- **Security badges** and guarantees
- **Plan pricing** with discounts
- **Navigation** back to landing

### 3. Configuration Page (`configuration.html`)
- **3-step wizard** with progress indicator
- **Step 1**: Company information form
- **Step 2**: Trading configuration options  
- **Step 3**: Trading instruments selection
- **Form validation** and navigation
- **Progress tracking**

### 4. Dashboard Page (`dashboard.html`)
- **Company-specific welcome** section
- **Platform access credentials** (customizable domains)
- **Quick start guide** with 4 steps
- **Trading configuration** summary
- **Support resources** and external links
- **Credential management** (show/hide, copy to clipboard)

## 🔧 Interactive Features

### Form Navigation
- **Multi-step wizard** in configuration page
- **Form validation** with error messages
- **Progress indicators** and step tracking
- **Data persistence** between pages using localStorage

### Credential Management  
- **Show/hide credentials** functionality
- **Copy to clipboard** for usernames/passwords
- **Toast notifications** for user feedback
- **Dynamic email generation** based on company name

### Responsive Design
- **Mobile-first** responsive layout
- **Flexible grid systems** (1-3 columns based on screen size)
- **Touch-friendly** buttons and interactions
- **Optimized** for all device sizes

## 🎨 Design System

### Visual Style
- **Dark theme** optimized for professional trading environments
- **Gradient backgrounds** (slate-900 to blue-900)
- **Blue-cyan gradients** for primary actions
- **Card-based layout** with subtle borders and shadows

### Typography
- **Modern sans-serif** font stack
- **Hierarchical sizing** (xs to 7xl)
- **Consistent line heights** and spacing
- **High contrast** for readability

### Components
- **shadcn/ui inspired** component design
- **Consistent button styles** (primary, secondary, ghost)
- **Badge system** for status indicators
- **Modal dialogs** for additional content
- **Toast notifications** for feedback

## 🔗 External Integrations

The demo includes links to external services that can be customized:

- **CRM Portal**: Link to client management system
- **Dealer Terminal**: Link to trading platform  
- **Documentation**: Link to user guides
- **Support Email**: Mailto link for customer support
- **Training Calendar**: Link to scheduling system

## 📱 Browser Compatibility

- **Chrome 90+**
- **Firefox 88+** 
- **Safari 14+**
- **Edge 90+**

## 🚀 Deployment

### Local Development
1. Open `index.html` directly in browser
2. No server required for basic functionality
3. Modern browsers support all features

### Web Hosting
1. Upload entire `html/` folder to web server
2. Set `index.html` as default page
3. Ensure proper MIME types for `.js` and `.css` files

### Content Delivery Network (CDN)
- All assets are self-contained
- No external dependencies
- Fast loading times
- Works offline

## 🎯 Customization Examples

### Example 1: Forex Broker
```javascript
// In config.js
company: {
    name: "ForexPro Trading",
    tagline: "Professional Forex Solutions",
    logo: "FPT"
}

plans: [
    {
        name: "Trader",
        price: "$1,999",
        description: "Perfect for individual forex traders"
    }
]
```

### Example 2: Crypto Exchange
```javascript
// In config.js  
company: {
    name: "CryptoMax Exchange",
    tagline: "Advanced Cryptocurrency Trading",
    logo: "CMX"
}

features: [
    {
        icon: "₿",
        title: "Multi-Crypto Support",
        description: "Trade Bitcoin, Ethereum, and 200+ cryptocurrencies"
    }
]
```

### Example 3: Investment Platform
```javascript
// In config.js
company: {
    name: "WealthBuilder Pro", 
    tagline: "Smart Investment Solutions",
    logo: "WBP"
}

plans: [
    {
        name: "Portfolio",
        price: "$4,999", 
        description: "Complete portfolio management platform"
    }
]
```

## 🔧 Advanced Customization

### Adding New Features
1. Add feature object to `BrokerConfig.features` array
2. Feature will automatically appear on landing page
3. Include icon (emoji or text) and description

### Modifying Pricing Tiers
1. Edit `BrokerConfig.plans` array
2. Add/remove plans as needed
3. Set `popular: true` for recommended plan
4. Features array populates automatically

### Changing External URLs
1. Update `BrokerConfig.platforms` for CRM/Dealer links
2. Modify `BrokerConfig.support` for documentation/support
3. All links update automatically throughout demo

### Custom Styling
1. Edit CSS variables in `:root` section
2. Modify component classes in `styles.css`
3. Add custom animations or transitions

## 📞 Support

For questions about customizing this demo:
- Review the `config.js` file comments
- Check browser console for any errors
- Ensure all file paths are correct
- Test in multiple browsers

## 📄 License

This demo is designed for customization and white-label use. Modify as needed for your trading platform business.

---

**🎯 Remember**: The main customization happens in `assets/config.js` - edit this one file to rebrand the entire demo!