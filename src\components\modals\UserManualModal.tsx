import React from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Download, BookOpen, FileText, Users, Monitor, Shield, Clock, Globe, Eye, AlertTriangle, Lightbulb } from 'lucide-react';
import { BaseModalProps } from '@/types/trading';

interface UserManualModalProps extends BaseModalProps {}

const UserManualModal: React.FC<UserManualModalProps> = ({ isOpen, onClose }) => {
  
  const downloadPDF = () => {
    // Create a simple PDF-like content for download
    const content = `
OTX Dealer Platform User Manual - PDF Version
Generated on: ${new Date().toLocaleDateString()}

This is a simplified PDF version of the user manual.
For the complete interactive manual, please use the platform interface.

Contact support for the full PDF documentation.
    `;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'OTX_Dealer_Platform_User_Manual.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] bg-slate-900 border-slate-700">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center text-2xl">
            <BookOpen className="w-6 h-6 mr-3 text-blue-400" />
            OTX Dealer Platform User Manual
          </DialogTitle>
          <DialogDescription className="text-gray-400 flex items-center justify-between">
            <span>Complete guide to using the dealer platform</span>
            <div className="flex items-center space-x-2">
              <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                Version 1.0
              </Badge>
              <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                2025-07-30
              </Badge>
            </div>
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col space-y-4">
          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button
              onClick={downloadPDF}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Download className="w-4 h-4 mr-2" />
              Download PDF
            </Button>
          </div>

          <ScrollArea className="h-[600px] w-full rounded-md border border-slate-700 p-6 bg-slate-800/50">
            <div className="space-y-6 text-gray-300">
              
              {/* Table of Contents */}
              <div className="space-y-4">
                <h2 className="text-xl font-bold text-white flex items-center">
                  <FileText className="w-5 h-5 mr-2 text-blue-400" />
                  Table of Contents
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer">
                      <span className="font-mono text-sm mr-2">1.</span>
                      <span>Introduction</span>
                    </div>
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer ml-4">
                      <span className="font-mono text-sm mr-2">1.1</span>
                      <span>Welcome</span>
                    </div>
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer ml-4">
                      <span className="font-mono text-sm mr-2">1.2</span>
                      <span>About this Platform</span>
                    </div>
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer ml-4">
                      <span className="font-mono text-sm mr-2">1.3</span>
                      <span>How to Use this Manual</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer">
                      <span className="font-mono text-sm mr-2">2.</span>
                      <span>Getting Started</span>
                    </div>
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer ml-4">
                      <span className="font-mono text-sm mr-2">2.1</span>
                      <span>System Requirements</span>
                    </div>
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer ml-4">
                      <span className="font-mono text-sm mr-2">2.2</span>
                      <span>Account Registration & Login</span>
                    </div>
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer ml-4">
                      <span className="font-mono text-sm mr-2">2.3</span>
                      <span>Dashboard Overview</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer">
                      <span className="font-mono text-sm mr-2">3.</span>
                      <span>Core Features</span>
                    </div>
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer ml-4">
                      <span className="font-mono text-sm mr-2">3.1</span>
                      <span>Product Catalog</span>
                    </div>
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer ml-4">
                      <span className="font-mono text-sm mr-2">3.2</span>
                      <span>Order Management</span>
                    </div>
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer ml-4">
                      <span className="font-mono text-sm mr-2">3.3</span>
                      <span>Account Management</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer">
                      <span className="font-mono text-sm mr-2">4.</span>
                      <span>FAQ</span>
                    </div>
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer">
                      <span className="font-mono text-sm mr-2">5.</span>
                      <span>Support & Contact</span>
                    </div>
                    <div className="flex items-center text-blue-400 hover:text-blue-300 cursor-pointer">
                      <span className="font-mono text-sm mr-2">6.</span>
                      <span>Appendix</span>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="bg-slate-700" />

              {/* Introduction */}
              <div className="space-y-4">
                <h2 className="text-xl font-bold text-white flex items-center">
                  <Users className="w-5 h-5 mr-2 text-green-400" />
                  1. Introduction
                </h2>
                
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-white">1.1 Welcome</h3>
                  <p className="text-gray-300 leading-relaxed">
                    Welcome to the OTX Dealer Platform - your professional gateway to comprehensive financial trading operations. 
                    This platform has been designed specifically for business dealers who require reliable, real-time access to 
                    global markets with institutional-grade tools and features.
                  </p>
                  
                  <p className="text-gray-300 leading-relaxed">
                    Whether you're managing client portfolios, executing complex trading strategies, or monitoring market positions 
                    across multiple instruments, the OTX Dealer Platform provides the professional infrastructure you need to operate 
                    efficiently in today's fast-paced financial markets.
                  </p>
                </div>

                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-white">1.2 About this Platform</h3>
                  <p className="text-gray-300 leading-relaxed">
                    The OTX Dealer Platform is a comprehensive web-based trading terminal that enables 24/7 market access and 
                    portfolio management. The platform's core value propositions include:
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div className="bg-slate-800/30 p-4 rounded-lg border border-slate-700">
                      <h4 className="font-semibold text-blue-400 flex items-center mb-2">
                        <Globe className="w-4 h-4 mr-2" />
                        Real-Time Market Access
                      </h4>
                      <ul className="text-sm text-gray-400 space-y-1">
                        <li>• Live pricing feeds for forex, commodities, cryptocurrencies</li>
                        <li>• Instant order execution with professional-grade tools</li>
                        <li>• Multi-instrument portfolio management capabilities</li>
                      </ul>
                    </div>
                    
                    <div className="bg-slate-800/30 p-4 rounded-lg border border-slate-700">
                      <h4 className="font-semibold text-green-400 flex items-center mb-2">
                        <Monitor className="w-4 h-4 mr-2" />
                        Professional Trading Tools
                      </h4>
                      <ul className="text-sm text-gray-400 space-y-1">
                        <li>• Advanced position and order management system</li>
                        <li>• Comprehensive risk management features</li>
                        <li>• Real-time profit/loss calculations</li>
                      </ul>
                    </div>
                    
                    <div className="bg-slate-800/30 p-4 rounded-lg border border-slate-700">
                      <h4 className="font-semibold text-purple-400 flex items-center mb-2">
                        <Shield className="w-4 h-4 mr-2" />
                        Compliance & Audit Features
                      </h4>
                      <ul className="text-sm text-gray-400 space-y-1">
                        <li>• Complete audit trail for all trading activities</li>
                        <li>• Regulatory-compliant record keeping</li>
                        <li>• Multi-level user access controls</li>
                      </ul>
                    </div>
                    
                    <div className="bg-slate-800/30 p-4 rounded-lg border border-slate-700">
                      <h4 className="font-semibold text-yellow-400 flex items-center mb-2">
                        <Users className="w-4 h-4 mr-2" />
                        Account Management
                      </h4>
                      <ul className="text-sm text-gray-400 space-y-1">
                        <li>• Multi-account oversight for client portfolios</li>
                        <li>• Detailed transaction history and analytics</li>
                        <li>• Integrated margin monitoring and risk alerts</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="bg-slate-700" />

              {/* Getting Started */}
              <div className="space-y-4">
                <h2 className="text-xl font-bold text-white flex items-center">
                  <Monitor className="w-5 h-5 mr-2 text-blue-400" />
                  2. Getting Started
                </h2>
                
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-white">2.1 System Requirements</h3>
                  
                  <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-400 mb-2">Supported Web Browsers</h4>
                    <ul className="text-sm text-gray-300 space-y-1">
                      <li>• Google Chrome (version 90 or higher)</li>
                      <li>• Mozilla Firefox (version 88 or higher)</li>
                      <li>• Safari (version 14 or higher)</li>
                      <li>• Microsoft Edge (version 90 or higher)</li>
                    </ul>
                  </div>
                  
                  <div className="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                    <h4 className="font-semibold text-green-400 mb-2">System Specifications</h4>
                    <ul className="text-sm text-gray-300 space-y-1">
                      <li>• RAM: 8GB minimum (16GB recommended)</li>
                      <li>• Screen Resolution: 1920x1080 minimum</li>
                      <li>• Internet: Stable broadband (10Mbps minimum)</li>
                      <li>• OS: Windows 10+, macOS 10.15+, or modern Linux</li>
                    </ul>
                  </div>
                  
                  <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4 flex items-start space-x-3">
                    <Lightbulb className="w-5 h-5 text-yellow-400 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-yellow-400 mb-1">Pro Tip</h4>
                      <p className="text-sm text-gray-300">
                        For professional trading environments, consider using multiple monitors to maximize workspace 
                        efficiency and enable simultaneous monitoring of different market data panels.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-white">2.2 Account Login</h3>
                  
                  <div className="bg-slate-800/30 p-4 rounded-lg border border-slate-600">
                    <h4 className="font-semibold text-white mb-2">Demo Access Credentials</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-gray-400">Username:</span>
                        <code className="block text-green-400 bg-slate-700 px-2 py-1 rounded mt-1">dealer01</code>
                      </div>
                      <div>
                        <span className="text-sm text-gray-400">Password:</span>
                        <code className="block text-green-400 bg-slate-700 px-2 py-1 rounded mt-1">password123</code>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-red-900/20 border border-red-600/30 rounded-lg p-4 flex items-start space-x-3">
                    <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-red-400 mb-1">Important</h4>
                      <p className="text-sm text-gray-300">
                        Always complete the CAPTCHA verification before attempting to log in. The system will reject 
                        login attempts without proper CAPTCHA completion.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="bg-slate-700" />

              {/* Quick Reference */}
              <div className="space-y-4">
                <h2 className="text-xl font-bold text-white flex items-center">
                  <Eye className="w-5 h-5 mr-2 text-purple-400" />
                  Quick Reference
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-slate-800/30 p-4 rounded-lg border border-slate-700">
                    <h4 className="font-semibold text-white mb-3">Common Shortcuts</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">New Position:</span>
                        <code className="text-blue-400 bg-slate-700 px-2 py-0.5 rounded">Ctrl+N</code>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Quick Order:</span>
                        <code className="text-blue-400 bg-slate-700 px-2 py-0.5 rounded">Double-click symbol</code>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Refresh Data:</span>
                        <code className="text-blue-400 bg-slate-700 px-2 py-0.5 rounded">F5</code>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-slate-800/30 p-4 rounded-lg border border-slate-700">
                    <h4 className="font-semibold text-white mb-3">Dashboard Panels</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
                        <span className="text-gray-300">Dealer Analytics (Top-Left)</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
                        <span className="text-gray-300">Trader Info (Top-Right)</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-yellow-500 rounded mr-2"></div>
                        <span className="text-gray-300">Price Quotes (Bottom-Left)</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-purple-500 rounded mr-2"></div>
                        <span className="text-gray-300">Main Data (Bottom-Right)</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="bg-slate-700" />

              {/* Contact Support */}
              <div className="space-y-4">
                <h2 className="text-xl font-bold text-white flex items-center">
                  <Clock className="w-5 h-5 mr-2 text-cyan-400" />
                  Need More Help?
                </h2>
                
                <div className="bg-cyan-900/20 border border-cyan-600/30 rounded-lg p-4">
                  <p className="text-gray-300 mb-3">
                    This is a preview of the complete user manual. For detailed instructions, troubleshooting, 
                    and advanced features, download the full PDF version or contact our support team.
                  </p>
                  
                  <div className="flex flex-wrap gap-2">
                    <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                      Complete Guide Available
                    </Badge>
                    <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                      24/7 Support
                    </Badge>
                    <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                      Video Tutorials
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UserManualModal;